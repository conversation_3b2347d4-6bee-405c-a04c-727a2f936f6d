import mongoose from "mongoose";

/**
 * Enhanced Voice Assignment Model - Production-grade assignment tracking
 * Comprehensive validation, error handling, and performance optimizations
 */

// Enhanced validation functions
const validateDeadline = function(deadline) {
  if (!deadline) return true; // Optional field
  const now = new Date();
  const assignedAt = this.assignedAt || now;
  return new Date(deadline) > assignedAt;
};

const validatePriority = function(priority) {
  return !priority || ['low', 'medium', 'high', 'urgent'].includes(priority);
};

const validateStatus = function(status) {
  return ['assigned', 'in_progress', 'recorded', 'submitted', 'approved', 'rejected', 'cancelled'].includes(status);
};

const validateRecordingUrl = function(url) {
  if (!url) return true;
  const urlPattern = /^(https?:\/\/)|(^\/)/;
  return urlPattern.test(url);
};

const validateAudioExtension = function(filename) {
  if (!filename) return true;
  return /\.(mp3|wav|m4a|aac|ogg|flac)$/i.test(filename);
};

const validateBitrate = function(bitrate) {
  if (!bitrate) return true;
  return /^\d+(kbps|Kbps|KBPS)$/i.test(bitrate);
};

const voiceAssignmentSchema = new mongoose.Schema(
  {
    // Enhanced core assignment relationships with validation
    questionIds: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: "QuestionTemplate",
      required: [true, 'At least one question is required'],
      validate: {
        validator: function(v) {
          return mongoose.Types.ObjectId.isValid(v);
        },
        message: 'Invalid question ID format'
      }
    }],
    title: {
      type: String,
      required: [true, "Assignment title is required"],
      trim: true,
      minlength: [3, "Assignment title must be at least 3 characters"],
      maxlength: [200, "Assignment title cannot exceed 200 characters"],
      validate: {
        validator: function(v) {
          return /^[a-zA-Z0-9\s\-_.,!?()@]+$/.test(v);
        },
        message: 'Title contains invalid characters'
      }
    },
    description: {
      type: String,
      trim: true,
      maxlength: [1000, "Assignment description cannot exceed 1000 characters"],
      validate: {
        validator: function(v) {
          return !v || v.length >= 2;
        },
        message: 'Description must be at least 2 characters if provided'
      }
    },
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: [true, "Model ID is required"],
      index: true,
      validate: {
        validator: function(v) {
          return mongoose.Types.ObjectId.isValid(v);
        },
        message: 'Invalid model ID format'
      }
    },
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: [true, "Agency ID is required"],
      index: true,
      validate: {
        validator: function(v) {
          return mongoose.Types.ObjectId.isValid(v);
        },
        message: 'Invalid agency ID format'
      }
    },

    // Assignment metadata
    assignedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: [true, "Assigned by is required"],
      validate: {
        validator: function(value) {
          return value.toString() === this.agencyId.toString();
        },
        message: "Assigned by must match agency ID"
      }
    },
    assignedAt: {
      type: Date,
      default: Date.now,
      index: true,
    },

    // Enhanced assignment status and lifecycle with validation
    status: {
      type: String,
      enum: {
        values: [
          "assigned", // Just assigned, not started
          "in_progress", // Model has started working
          "recorded", // Audio recorded but not submitted
          "submitted", // Submitted for review
          "approved", // Approved by agency
          "rejected", // Rejected, needs rework
          "cancelled", // Assignment cancelled
        ],
        message: 'Invalid assignment status'
      },
      default: "assigned",
      index: true,
      validate: {
        validator: validateStatus,
        message: 'Status must be a valid assignment status'
      }
    },
    statusHistory: [{
      status: {
        type: String,
        required: true,
        validate: {
          validator: validateStatus,
          message: 'Invalid status in history'
        }
      },
      timestamp: {
        type: Date,
        default: Date.now,
        required: true
      },
      updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      reason: {
        type: String,
        maxlength: [500, 'Status change reason cannot exceed 500 characters']
      },
      metadata: {
        type: mongoose.Schema.Types.Mixed,
        default: {}
      }
    }],

    // Enhanced timing and deadlines with validation
    deadline: {
      type: Date,
      index: true,
      validate: {
        validator: validateDeadline,
        message: "Deadline must be in the future and after assignment date"
      }
    },
    deadlineReminders: [{
      reminderTime: {
        type: Date,
        required: true
      },
      sent: {
        type: Boolean,
        default: false
      },
      sentAt: Date
    }],
    startedAt: {
      type: Date,
      default: null,
    },
    completedAt: {
      type: Date,
      default: null,
    },

    // Enhanced priority with validation
    priority: {
      type: String,
      enum: {
        values: ["low", "medium", "high", "urgent"],
        message: 'Priority must be one of: low, medium, high, urgent'
      },
      default: "medium",
      index: true,
      validate: {
        validator: validatePriority,
        message: 'Invalid priority level'
      }
    },
    priorityReason: {
      type: String,
      maxlength: [200, 'Priority reason cannot exceed 200 characters'],
      trim: true
    },

    // Enhanced recording and submission details
    recordingUrl: {
      type: String,
      default: null,
      trim: true,
      validate: {
        validator: validateRecordingUrl,
        message: "Recording URL must be a valid URL or file path"
      }
    },
    recordingBackupUrls: [{
      url: {
        type: String,
        required: true,
        validate: {
          validator: validateRecordingUrl,
          message: "Backup URL must be valid"
        }
      },
      createdAt: {
        type: Date,
        default: Date.now
      },
      type: {
        type: String,
        enum: ['backup', 'mirror', 'cdn'],
        default: 'backup'
      }
    }],
    recordingDuration: {
      type: Number, // in seconds
      default: null,
      min: [0, "Recording duration cannot be negative"],
      max: [3600, "Recording duration cannot exceed 1 hour"],
    },
    recordingMetadata: {
      filename: {
        type: String,
        trim: true,
        validate: {
          validator: validateAudioExtension,
          message: "Filename must have a valid audio extension (.mp3, .wav, .m4a, .aac, .ogg, .flac)"
        }
      },
      originalFilename: {
        type: String,
        trim: true
      },
      fileSize: {
        type: Number, // in bytes
        min: [0, "File size cannot be negative"],
        max: [209715200, "File size cannot exceed 200MB"], // Increased to 200MB
        validate: {
          validator: function(v) {
            return v === undefined || (Number.isInteger(v) && v >= 0);
          },
          message: 'File size must be a non-negative integer'
        }
      },
      format: {
        type: String,
        enum: {
          values: ["mp3", "wav", "m4a", "aac", "ogg", "flac"],
          message: 'Audio format must be one of: mp3, wav, m4a, aac, ogg, flac'
        },
        lowercase: true
      },
      bitrate: {
        type: String,
        validate: {
          validator: validateBitrate,
          message: "Bitrate must be in format like '128kbps'"
        }
      },
      sampleRate: {
        type: Number,
        enum: {
          values: [22050, 44100, 48000, 96000, 192000],
          message: 'Sample rate must be one of: 22050, 44100, 48000, 96000, 192000'
        }
      },
      channels: {
        type: Number,
        enum: [1, 2], // Mono or Stereo
        default: 1
      },
      codec: {
        type: String,
        trim: true
      },
      uploadedAt: {
        type: Date,
        default: Date.now,
        immutable: true
      },
      checksum: {
        type: String,
        trim: true
      },
      processingStatus: {
        type: String,
        enum: ['pending', 'processing', 'completed', 'failed'],
        default: 'pending'
      }
    },

    // Enhanced review and feedback with comprehensive validation
    reviewedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      default: null,
      validate: {
        validator: function(v) {
          return !v || mongoose.Types.ObjectId.isValid(v);
        },
        message: 'Invalid reviewer ID format'
      }
    },
    reviewedAt: {
      type: Date,
      default: null,
      validate: {
        validator: function(v) {
          return !v || v <= new Date();
        },
        message: 'Review date cannot be in the future'
      }
    },
    reviewComments: {
      type: String,
      trim: true,
      maxlength: [2000, "Review comments cannot exceed 2000 characters"],
      validate: {
        validator: function(v) {
          // If status is rejected, comments are required
          if (this.status === 'rejected' && (!v || v.trim().length < 10)) {
            return false;
          }
          return true;
        },
        message: 'Review comments are required for rejected assignments and must be at least 10 characters'
      }
    },
    rating: {
      type: Number,
      min: [1, "Rating must be at least 1"],
      max: [5, "Rating cannot exceed 5"],
      default: null,
      validate: {
        validator: function(value) {
          return value === null || (Number.isInteger(value) && value >= 1 && value <= 5);
        },
        message: "Rating must be a whole number between 1 and 5"
      }
    },
    detailedFeedback: {
      audioQuality: {
        type: Number,
        min: 1,
        max: 5,
        validate: {
          validator: function(v) {
            return !v || Number.isInteger(v);
          },
          message: 'Audio quality rating must be an integer'
        }
      },
      voiceClarity: {
        type: Number,
        min: 1,
        max: 5,
        validate: {
          validator: function(v) {
            return !v || Number.isInteger(v);
          },
          message: 'Voice clarity rating must be an integer'
        }
      },
      emotionalDelivery: {
        type: Number,
        min: 1,
        max: 5,
        validate: {
          validator: function(v) {
            return !v || Number.isInteger(v);
          },
          message: 'Emotional delivery rating must be an integer'
        }
      },
      technicalQuality: {
        type: Number,
        min: 1,
        max: 5,
        validate: {
          validator: function(v) {
            return !v || Number.isInteger(v);
          },
          message: 'Technical quality rating must be an integer'
        }
      },
      overallSatisfaction: {
        type: Number,
        min: 1,
        max: 5,
        validate: {
          validator: function(v) {
            return !v || Number.isInteger(v);
          },
          message: 'Overall satisfaction rating must be an integer'
        }
      }
    },

    // Enhanced revision tracking with validation
    revisionCount: {
      type: Number,
      default: 0,
      min: [0, 'Revision count cannot be negative'],
      max: [10, 'Maximum 10 revisions allowed'],
      validate: {
        validator: function(v) {
          return Number.isInteger(v) && v >= 0;
        },
        message: 'Revision count must be a non-negative integer'
      }
    },
    maxRevisions: {
      type: Number,
      default: 3,
      min: [1, 'At least 1 revision must be allowed'],
      max: [10, 'Maximum 10 revisions allowed'],
      validate: {
        validator: function(v) {
          return Number.isInteger(v) && v >= 1;
        },
        message: 'Max revisions must be a positive integer'
      }
    },
    revisionHistory: [{
      submittedAt: {
        type: Date,
        required: true,
        default: Date.now
      },
      recordingUrl: {
        type: String,
        required: true,
        trim: true,
        validate: {
          validator: validateRecordingUrl,
          message: 'Invalid recording URL in revision history'
        }
      },
      reviewComments: {
        type: String,
        required: true,
        trim: true,
        minlength: [10, 'Review comments must be at least 10 characters'],
        maxlength: [2000, 'Review comments cannot exceed 2000 characters']
      },
      rejectedAt: {
        type: Date,
        required: true,
        default: Date.now
      },
      rejectedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Agency",
        required: true,
        validate: {
          validator: function(v) {
            return mongoose.Types.ObjectId.isValid(v);
          },
          message: 'Invalid rejector ID format'
        }
      },
      revisionNumber: {
        type: Number,
        required: true,
        min: 1
      },
      metadata: {
        type: mongoose.Schema.Types.Mixed,
        default: {}
      }
    }],

    // Model feedback and notes
    modelNotes: {
      type: String,
      trim: true,
      maxlength: [500, "Model notes cannot exceed 500 characters"],
    },

    // Enhanced assignment settings with validation
    allowRevisions: {
      type: Boolean,
      default: true,
      index: true
    },
    autoApprove: {
      type: Boolean,
      default: false,
      validate: {
        validator: function(v) {
          // Auto-approve should not be enabled for high-priority assignments
          if (v === true && this.priority === 'urgent') {
            return false;
          }
          return true;
        },
        message: 'Auto-approve cannot be enabled for urgent priority assignments'
      }
    },
    requiresManualReview: {
      type: Boolean,
      default: false
    },
    qualityThreshold: {
      type: Number,
      min: 1,
      max: 5,
      default: 3,
      validate: {
        validator: function(v) {
          return Number.isInteger(v);
        },
        message: 'Quality threshold must be an integer between 1 and 5'
      }
    },

    // Recording submission tracking
    recordings: [{
      questionId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "QuestionTemplate",
        required: true,
      },
      questionText: String,    // Store question text for reference
      questionTags: [String],  // Store question tags for filtering and display
      audioUrl: String,        // Path to uploaded audio file
      originalFileName: String, // Original file name
      duration: Number,        // Duration in seconds
      fileSize: Number,        // File size in bytes
      uploadedAt: { type: Date, default: Date.now },
      status: {
        type: String,
        enum: ["pending", "submitted", "approved", "rejected"],
        default: "pending"
      },
      reviewComments: String,
      reviewedAt: Date,
      reviewedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Agency",
      },
    }],
    submittedAt: {
      type: Date,
      default: null
    },
    downloadCount: {
      type: Number,
      default: 0
    },
    lastDownloadedAt: {
      type: Date,
      default: null
    },

    // Soft delete
    isDeleted: {
      type: Boolean,
      default: false,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  {
    timestamps: true,
  }
);

// Enhanced performance indexes for optimal query performance
voiceAssignmentSchema.index({ modelId: 1, status: 1, assignedAt: -1 });
voiceAssignmentSchema.index({ agencyId: 1, status: 1, priority: -1 });
voiceAssignmentSchema.index({ agencyId: 1, createdAt: -1 });
voiceAssignmentSchema.index({ status: 1, deadline: 1, priority: -1 });
voiceAssignmentSchema.index({ assignedAt: -1 });
voiceAssignmentSchema.index({ isDeleted: 1, status: 1 });
voiceAssignmentSchema.index({ deadline: 1, status: 1 }); // For overdue queries
voiceAssignmentSchema.index({ priority: -1, assignedAt: -1 }); // For priority sorting
voiceAssignmentSchema.index({ reviewedAt: -1 }); // For review analytics
voiceAssignmentSchema.index({ completedAt: -1 }); // For completion analytics
voiceAssignmentSchema.index({ 'recordingMetadata.processingStatus': 1 }); // For processing queries

// Compound index for unique assignments (prevent duplicate assignments)
voiceAssignmentSchema.index(
  { agencyId: 1, modelId: 1, title: 1 },
  { 
    unique: true, 
    partialFilterExpression: { isDeleted: false },
    name: 'unique_assignment_per_model'
  }
);

// Compound index for efficient filtering and sorting
voiceAssignmentSchema.index(
  { agencyId: 1, status: 1, priority: -1, assignedAt: -1 },
  { name: 'agency_status_priority_date' }
);

// Enhanced text search index
voiceAssignmentSchema.index({
  title: "text",
  description: "text",
  modelNotes: "text",
  reviewComments: "text"
}, {
  name: 'assignment_text_search',
  weights: {
    title: 10,
    description: 5,
    modelNotes: 3,
    reviewComments: 1
  }
});

// Enhanced virtual properties with better calculations
voiceAssignmentSchema.virtual("timeElapsed").get(function () {
  const now = new Date();
  const assigned = this.assignedAt;
  const elapsed = now - assigned;
  return {
    hours: Math.floor(elapsed / (1000 * 60 * 60)),
    minutes: Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60)),
    totalMinutes: Math.floor(elapsed / (1000 * 60)),
    totalHours: Math.floor(elapsed / (1000 * 60 * 60))
  };
});

// Virtual for time remaining until deadline
voiceAssignmentSchema.virtual("timeRemaining").get(function () {
  if (!this.deadline) return null;
  const now = new Date();
  const remaining = this.deadline - now;
  
  if (remaining <= 0) {
    return {
      isOverdue: true,
      hours: 0,
      minutes: 0,
      totalMinutes: 0,
      overdueBy: Math.abs(Math.floor(remaining / (1000 * 60 * 60)))
    };
  }
  
  return {
    isOverdue: false,
    hours: Math.floor(remaining / (1000 * 60 * 60)),
    minutes: Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60)),
    totalMinutes: Math.floor(remaining / (1000 * 60)),
    totalHours: Math.floor(remaining / (1000 * 60 * 60))
  };
});

// Virtual for completion time (if completed)
voiceAssignmentSchema.virtual("completionTime").get(function () {
  if (!this.completedAt) return null;
  const duration = this.completedAt - this.assignedAt;
  return {
    minutes: Math.floor(duration / (1000 * 60)),
    hours: Math.floor(duration / (1000 * 60 * 60)),
    days: Math.floor(duration / (1000 * 60 * 60 * 24)),
    totalMinutes: Math.floor(duration / (1000 * 60))
  };
});

// Enhanced overdue status virtual
voiceAssignmentSchema.virtual("isOverdue").get(function () {
  if (
    !this.deadline ||
    ['approved', 'cancelled'].includes(this.status)
  ) {
    return false;
  }
  return new Date() > this.deadline;
});

// Virtual for assignment progress percentage
voiceAssignmentSchema.virtual("progressPercentage").get(function () {
  const statusProgress = {
    'assigned': 0,
    'in_progress': 25,
    'recorded': 50,
    'submitted': 75,
    'approved': 100,
    'rejected': 40, // Back to revision
    'cancelled': 0
  };
  return statusProgress[this.status] || 0;
});

// Virtual for average rating from detailed feedback
voiceAssignmentSchema.virtual("averageDetailedRating").get(function () {
  if (!this.detailedFeedback) return null;
  
  const ratings = [
    this.detailedFeedback.audioQuality,
    this.detailedFeedback.voiceClarity,
    this.detailedFeedback.emotionalDelivery,
    this.detailedFeedback.technicalQuality,
    this.detailedFeedback.overallSatisfaction
  ].filter(rating => rating !== null && rating !== undefined);
  
  if (ratings.length === 0) return null;
  
  return Math.round((ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length) * 10) / 10;
});

// Enhanced pre-save middleware with comprehensive status tracking
voiceAssignmentSchema.pre("save", function (next) {
  const now = new Date();
  
  // Track status changes in history
  if (this.isModified("status")) {
    // Add to status history if not already present
    const lastHistoryEntry = this.statusHistory[this.statusHistory.length - 1];
    if (!lastHistoryEntry || lastHistoryEntry.status !== this.status) {
      this.statusHistory.push({
        status: this.status,
        timestamp: now,
        updatedBy: this.reviewedBy || this.assignedBy,
        reason: this.statusChangeReason || 'Status updated',
        metadata: {
          previousStatus: this.constructor.findOne({ _id: this._id }).status,
          revisionCount: this.revisionCount
        }
      });
    }
    
    // Track when work started
    if (this.status === "in_progress" && !this.startedAt) {
      this.startedAt = now;
    }
    
    // Track completion time
    if (["approved", "cancelled"].includes(this.status) && !this.completedAt) {
      this.completedAt = now;
    }
    
    // Track review time
    if (["approved", "rejected"].includes(this.status) && !this.reviewedAt) {
      this.reviewedAt = now;
    }
    
    // Auto-set reviewedBy if not set for review statuses
    if (["approved", "rejected"].includes(this.status) && !this.reviewedBy) {
      this.reviewedBy = this.assignedBy; // Fallback to assigner
    }
  }
  
  // Validate revision count doesn't exceed maximum
  if (this.revisionCount > this.maxRevisions) {
    return next(new Error(`Revision count (${this.revisionCount}) exceeds maximum allowed (${this.maxRevisions})`));
  }
  
  // Ensure deadline is in the future if being set
  if (this.isModified('deadline') && this.deadline && this.deadline <= now) {
    return next(new Error('Deadline must be in the future'));
  }
  
  // Auto-calculate priority if not set
  if (!this.priority) {
    const daysUntilDeadline = this.deadline ? Math.ceil((this.deadline - now) / (1000 * 60 * 60 * 24)) : null;
    if (daysUntilDeadline !== null) {
      if (daysUntilDeadline <= 1) this.priority = 'urgent';
      else if (daysUntilDeadline <= 3) this.priority = 'high';
      else if (daysUntilDeadline <= 7) this.priority = 'medium';
      else this.priority = 'low';
    }
  }
  
  next();
});

// Pre-save middleware for validation
voiceAssignmentSchema.pre('save', function(next) {
  // Ensure required fields for specific statuses
  if (this.status === 'submitted' && !this.recordingUrl) {
    return next(new Error('Recording URL is required for submitted assignments'));
  }
  
  if (this.status === 'rejected' && (!this.reviewComments || this.reviewComments.trim().length < 10)) {
    return next(new Error('Review comments are required for rejected assignments'));
  }
  
  next();
});

// Enhanced static methods for comprehensive querying
voiceAssignmentSchema.statics.findByModel = function (modelId, options = {}) {
  const query = { modelId, isDeleted: false };
  if (options.status) {
    query.status = Array.isArray(options.status) ? { $in: options.status } : options.status;
  }
  if (options.agencyId) query.agencyId = options.agencyId;
  if (options.priority) query.priority = options.priority;
  if (options.deadline) {
    query.deadline = options.deadline.before ? 
      { $lte: options.deadline.before } : 
      { $gte: options.deadline.after };
  }

  let queryBuilder = this.find(query)
    .populate("questionIds", "text sectionId tags")
    .populate("agencyId", "agencyName email")
    .populate("assignedBy", "name email")
    .populate("reviewedBy", "name email")
    .populate("modelId", "fullName username profilePicture");

  // Apply sorting
  const sortOptions = options.sort || { priority: -1, assignedAt: -1 };
  queryBuilder = queryBuilder.sort(sortOptions);

  // Apply pagination
  if (options.page && options.limit) {
    const skip = (options.page - 1) * options.limit;
    queryBuilder = queryBuilder.skip(skip);
  }
  queryBuilder = queryBuilder.limit(options.limit || 50);

  return queryBuilder;
};

// Enhanced static method to find assignments for an agency
voiceAssignmentSchema.statics.findByAgency = function (
  agencyId,
  options = {}
) {
  const query = { agencyId, isDeleted: false };
  if (options.status) {
    query.status = Array.isArray(options.status) ? { $in: options.status } : options.status;
  }
  if (options.modelId) query.modelId = options.modelId;
  if (options.priority) query.priority = options.priority;
  if (options.overdue) {
    query.deadline = { $lt: new Date() };
    query.status = { $in: ['assigned', 'in_progress', 'recorded', 'submitted'] };
  }

  let queryBuilder = this.find(query)
    .populate("questionIds", "text sectionId tags")
    .populate("modelId", "fullName username profilePicture")
    .populate("assignedBy", "name email")
    .populate("reviewedBy", "name email");

  // Apply sorting with priority consideration
  const sortOptions = options.sort || { priority: -1, assignedAt: -1 };
  queryBuilder = queryBuilder.sort(sortOptions);

  // Apply pagination
  if (options.page && options.limit) {
    const skip = (options.page - 1) * options.limit;
    queryBuilder = queryBuilder.skip(skip);
  }
  queryBuilder = queryBuilder.limit(options.limit || 50);

  return queryBuilder;
};

// Enhanced static method to get comprehensive assignment statistics
voiceAssignmentSchema.statics.getStats = function (agencyId, options = {}) {
  const matchStage = { agencyId, isDeleted: false };
  if (options.startDate) matchStage.assignedAt = { $gte: options.startDate };
  if (options.endDate) {
    matchStage.assignedAt = matchStage.assignedAt || {};
    matchStage.assignedAt.$lte = options.endDate;
  }
  if (options.modelId) matchStage.modelId = options.modelId;

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalAssignments: { $sum: 1 },
        completedAssignments: {
          $sum: { $cond: [{ $eq: ["$status", "approved"] }, 1, 0] },
        },
        rejectedAssignments: {
          $sum: { $cond: [{ $eq: ["$status", "rejected"] }, 1, 0] },
        },
        inProgressAssignments: {
          $sum: { $cond: [{ $in: ["$status", ["assigned", "in_progress", "recorded", "submitted"]] }, 1, 0] },
        },
        overdueAssignments: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $ne: ["$deadline", null] },
                  { $lt: ["$deadline", new Date()] },
                  { $in: ["$status", ["assigned", "in_progress", "recorded", "submitted"]] }
                ]
              },
              1,
              0
            ]
          }
        },
        avgCompletionTime: {
          $avg: {
            $cond: [
              { $eq: ["$status", "approved"] },
              {
                $divide: [
                  { $subtract: ["$completedAt", "$assignedAt"] },
                  1000 * 60, // Convert to minutes
                ],
              },
              null,
            ],
          },
        },
        averageRating: { $avg: "$rating" },
        averageDetailedRating: {
          $avg: {
            $cond: [
              { $ne: ["$detailedFeedback", null] },
              {
                $divide: [
                  {
                    $add: [
                      "$detailedFeedback.audioQuality",
                      "$detailedFeedback.voiceClarity",
                      "$detailedFeedback.emotionalDelivery",
                      "$detailedFeedback.technicalQuality",
                      "$detailedFeedback.overallSatisfaction"
                    ]
                  },
                  5
                ]
              },
              null
            ]
          }
        },
        totalRevisions: { $sum: "$revisionCount" },
        averageRevisions: { $avg: "$revisionCount" }
      },
    },
    {
      $addFields: {
        completionRate: {
          $cond: [
            { $gt: ["$totalAssignments", 0] },
            { $divide: ["$completedAssignments", "$totalAssignments"] },
            0
          ]
        },
        rejectionRate: {
          $cond: [
            { $gt: ["$totalAssignments", 0] },
            { $divide: ["$rejectedAssignments", "$totalAssignments"] },
            0
          ]
        }
      }
    }
  ]);
};

// New static method for finding overdue assignments
voiceAssignmentSchema.statics.findOverdue = function (agencyId = null) {
  const query = {
    deadline: { $lt: new Date() },
    status: { $in: ['assigned', 'in_progress', 'recorded', 'submitted'] },
    isDeleted: false
  };
  
  if (agencyId) query.agencyId = agencyId;
  
  return this.find(query)
    .populate("modelId", "fullName username")
    .populate("agencyId", "agencyName")
    .populate("assignedBy", "name email")
    .sort({ deadline: 1, priority: -1 });
};

// New static method for deadline monitoring
voiceAssignmentSchema.statics.findUpcomingDeadlines = function (hours = 24, agencyId = null) {
  const now = new Date();
  const futureTime = new Date(now.getTime() + (hours * 60 * 60 * 1000));
  
  const query = {
    deadline: { $gte: now, $lte: futureTime },
    status: { $in: ['assigned', 'in_progress', 'recorded', 'submitted'] },
    isDeleted: false
  };
  
  if (agencyId) query.agencyId = agencyId;
  
  return this.find(query)
    .populate("modelId", "fullName username")
    .populate("agencyId", "agencyName")
    .sort({ deadline: 1, priority: -1 });
};

// Enhanced instance methods for comprehensive assignment management
voiceAssignmentSchema.methods.canSubmit = function () {
  const validStatuses = ["assigned", "in_progress", "recorded", "rejected"];
  const notOverMaxRevisions = this.revisionCount < this.maxRevisions;
  const hasRecording = !!this.recordingUrl;
  
  return validStatuses.includes(this.status) && !this.isDeleted && notOverMaxRevisions && hasRecording;
};

// Instance method to check if assignment can be reviewed
voiceAssignmentSchema.methods.canReview = function () {
  return this.status === "submitted" && !this.isDeleted && !!this.recordingUrl;
};

// Instance method to check if assignment can be edited
voiceAssignmentSchema.methods.canEdit = function () {
  return ["assigned", "in_progress", "rejected"].includes(this.status) && !this.isDeleted;
};

// Instance method to check if assignment can be cancelled
voiceAssignmentSchema.methods.canCancel = function () {
  return !["approved", "cancelled"].includes(this.status) && !this.isDeleted;
};

// Enhanced instance method to add revision
voiceAssignmentSchema.methods.addRevision = function (
  recordingUrl,
  reviewComments,
  rejectedBy,
  metadata = {}
) {
  if (this.revisionCount >= this.maxRevisions) {
    throw new Error(`Maximum revisions (${this.maxRevisions}) exceeded`);
  }
  
  const revisionNumber = this.revisionHistory.length + 1;
  
  this.revisionHistory.push({
    submittedAt: this.updatedAt,
    recordingUrl: this.recordingUrl,
    reviewComments,
    rejectedAt: new Date(),
    rejectedBy,
    revisionNumber,
    metadata: {
      previousRecordingUrl: this.recordingUrl,
      previousRating: this.rating,
      ...metadata
    }
  });

  this.revisionCount += 1;
  this.recordingUrl = null;
  this.recordingDuration = null;
  this.recordingMetadata = {
    filename: null,
    originalFilename: null,
    fileSize: null,
    format: null,
    bitrate: null,
    sampleRate: null,
    channels: null,
    codec: null,
    checksum: null,
    processingStatus: 'pending'
  };
  
  this.status = this.revisionCount >= this.maxRevisions ? "cancelled" : "rejected";
  this.reviewedBy = rejectedBy;
  this.reviewedAt = new Date();
  this.reviewComments = reviewComments;
};

// Enhanced instance method to approve assignment
voiceAssignmentSchema.methods.approve = function (
  reviewedBy,
  rating = null,
  comments = null,
  detailedFeedback = null
) {
  if (!this.canReview()) {
    throw new Error('Assignment cannot be reviewed in current status');
  }
  
  this.status = "approved";
  this.reviewedBy = reviewedBy;
  this.reviewedAt = new Date();
  this.completedAt = new Date();
  this.rating = rating;
  this.reviewComments = comments;
  
  if (detailedFeedback) {
    this.detailedFeedback = detailedFeedback;
  }
  
  return this.save();
};

// New instance method to reject assignment
voiceAssignmentSchema.methods.reject = function (
  reviewedBy,
  reason,
  detailedFeedback = null,
  allowRevision = true
) {
  if (!this.canReview()) {
    throw new Error('Assignment cannot be reviewed in current status');
  }
  
  if (!allowRevision || this.revisionCount >= this.maxRevisions) {
    this.status = "cancelled";
    this.completedAt = new Date();
  } else {
    return this.addRevision(this.recordingUrl, reason, reviewedBy, { detailedFeedback });
  }
  
  this.reviewedBy = reviewedBy;
  this.reviewedAt = new Date();
  this.reviewComments = reason;
  
  if (detailedFeedback) {
    this.detailedFeedback = detailedFeedback;
  }
  
  return this.save();
};

// New instance method to submit recording
voiceAssignmentSchema.methods.submitRecording = function (recordingData) {
  if (!this.canSubmit()) {
    throw new Error('Assignment cannot be submitted in current status');
  }
  
  this.recordingUrl = recordingData.url;
  this.recordingDuration = recordingData.duration;
  this.recordingMetadata = {
    filename: recordingData.filename,
    originalFilename: recordingData.originalFilename,
    fileSize: recordingData.fileSize,
    format: recordingData.format,
    bitrate: recordingData.bitrate,
    sampleRate: recordingData.sampleRate,
    channels: recordingData.channels,
    codec: recordingData.codec,
    checksum: recordingData.checksum,
    processingStatus: 'completed'
  };
  
  this.status = "submitted";
  this.submittedAt = new Date();
  
  return this.save();
};

// New instance method to update progress
voiceAssignmentSchema.methods.updateProgress = function (status, notes = null) {
  const validTransitions = {
    'assigned': ['in_progress', 'cancelled'],
    'in_progress': ['recorded', 'assigned', 'cancelled'],
    'recorded': ['submitted', 'in_progress', 'cancelled'],
    'submitted': ['approved', 'rejected', 'cancelled'],
    'rejected': ['in_progress', 'cancelled'],
    'approved': [],
    'cancelled': []
  };
  
  if (!validTransitions[this.status].includes(status)) {
    throw new Error(`Invalid status transition from ${this.status} to ${status}`);
  }
  
  this.status = status;
  if (notes) {
    this.modelNotes = notes;
  }
  
  return this.save();
};

// New instance method to extend deadline
voiceAssignmentSchema.methods.extendDeadline = function (newDeadline, reason, updatedBy) {
  if (new Date(newDeadline) <= new Date()) {
    throw new Error('New deadline must be in the future');
  }
  
  const oldDeadline = this.deadline;
  this.deadline = new Date(newDeadline);
  
  // Add to status history
  this.statusHistory.push({
    status: this.status,
    timestamp: new Date(),
    updatedBy,
    reason: `Deadline extended: ${reason}`,
    metadata: {
      oldDeadline,
      newDeadline: this.deadline,
      extensionReason: reason
    }
  });
  
  return this.save();
};

// New instance method to get comprehensive time metrics
voiceAssignmentSchema.methods.getTimeMetrics = function () {
  const now = new Date();
  const assigned = this.assignedAt;
  
  const metrics = {
    timeElapsed: this.timeElapsed,
    timeRemaining: this.timeRemaining,
    completionTime: this.completionTime,
    isOverdue: this.isOverdue,
    progressPercentage: this.progressPercentage
  };
  
  // Add efficiency metrics
  if (this.completedAt) {
    const totalTime = this.completedAt - assigned;
    const expectedTime = this.deadline ? this.deadline - assigned : null;
    
    metrics.efficiency = expectedTime ? {
      completedEarly: this.completedAt < this.deadline,
      timeEfficiency: expectedTime > 0 ? (expectedTime - totalTime) / expectedTime : 0,
      daysAheadBehind: expectedTime ? Math.ceil((this.deadline - this.completedAt) / (1000 * 60 * 60 * 24)) : 0
    } : null;
  }
  
  return metrics;
};

const VoiceAssignment = mongoose.model(
  "VoiceAssignment",
  voiceAssignmentSchema
);
export default VoiceAssignment;