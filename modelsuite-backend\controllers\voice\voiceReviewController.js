import VoiceReview from "../../models/voice/VoiceReview.js";
import VoiceAssignment from "../../models/voice/VoiceAssignment.js";
import VoiceRecording from "../../models/voice/VoiceRecording.js";
import VoiceScript from "../../models/voice/VoiceScript.js";
import ModelUser from "../../models/model.js";
import Agency from "../../models/agency.js";
import { asyncHandler } from "../../utils/asyncHandler.js";
import { ApiError } from "../../utils/ApiError.js";
import { ApiResponse } from "../../utils/ApiResponse.js";
import mongoose from "mongoose";

/**
 * Create a review for a voice recording
 * @route POST /api/v1/voice/reviews
 * @access Agency only
 */
export const createReview = asyncHandler(async (req, res) => {
  const {
    assignmentId,
    recordingId,
    decision,
    overallRating,
    detailedRatings,
    comments,
    privateNotes,
    positiveAspects,
    improvementAreas,
    suggestions,
    qualityFlags,
    revisionRequirements,
    requiresSecondReview
  } = req.body;

  if (req.user.role !== 'agency') {
    throw new ApiError(403, "Only agencies can create reviews");
  }

  // Validate required fields
  if (!assignmentId || !decision) {
    throw new ApiError(400, "Assignment ID and decision are required");
  }

  if (!mongoose.Types.ObjectId.isValid(assignmentId)) {
    throw new ApiError(400, "Invalid assignment ID");
  }

  if (recordingId && !mongoose.Types.ObjectId.isValid(recordingId)) {
    throw new ApiError(400, "Invalid recording ID");
  }

  // Validate assignment
  const assignment = await VoiceAssignment.findById(assignmentId)
    .populate('scriptId', 'title category')
    .populate('modelId', 'fullName username');
  
  if (!assignment || assignment.isDeleted) {
    throw new ApiError(404, "Assignment not found");
  }

  if (assignment.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied - assignment not from your agency");
  }

  if (!assignment.canReview()) {
    throw new ApiError(400, "Assignment cannot be reviewed in its current status");
  }

  // Validate recording if provided
  let recording = null;
  if (recordingId) {
    recording = await VoiceRecording.findById(recordingId);
    if (!recording || recording.isDeleted) {
      throw new ApiError(404, "Recording not found");
    }
    if (recording.assignmentId.toString() !== assignmentId) {
      throw new ApiError(400, "Recording does not belong to this assignment");
    }
  } else {
    // Find the latest recording for this assignment
    recording = await VoiceRecording.findOne({
      assignmentId,
      isDeleted: false
    }).sort({ version: -1, uploadCompletedAt: -1 });
  }

  if (!recording) {
    throw new ApiError(400, "No recording found for this assignment");
  }

  // Check for existing review
  const existingReview = await VoiceReview.findOne({
    assignmentId,
    recordingId: recording._id,
    isDeleted: false
  });

  if (existingReview) {
    throw new ApiError(400, "Review already exists for this recording");
  }

  // Validate decision
  const validDecisions = ['approved', 'rejected', 'needs_revision', 'pending'];
  if (!validDecisions.includes(decision)) {
    throw new ApiError(400, "Invalid decision value");
  }

  // Create review
  const reviewData = {
    assignmentId,
    recordingId: recording._id,
    scriptId: assignment.scriptId._id,
    modelId: assignment.modelId._id,
    agencyId: req.user._id,
    reviewedBy: req.user._id,
    decision,
    overallRating: overallRating || null,
    detailedRatings: detailedRatings || {},
    comments: comments || '',
    privateNotes: privateNotes || '',
    positiveAspects: positiveAspects || [],
    improvementAreas: improvementAreas || [],
    suggestions: suggestions || [],
    qualityFlags: qualityFlags || {},
    revisionRequirements: revisionRequirements || [],
    requiresSecondReview: requiresSecondReview || false,
    reviewStartTime: new Date(),
    status: 'completed'
  };

  const review = await VoiceReview.create(reviewData);

  // Update assignment based on decision
  if (decision === 'approved') {
    await assignment.approve(req.user._id, comments, overallRating);
  } else if (decision === 'rejected') {
    assignment.status = 'rejected';
    assignment.reviewedBy = req.user._id;
    assignment.reviewedAt = new Date();
    assignment.reviewComments = comments;
    assignment.rating = overallRating;
    await assignment.save();
  } else if (decision === 'needs_revision') {
    await assignment.addRevision({
      reviewedBy: req.user._id,
      reason: comments,
      requirements: revisionRequirements,
      rating: overallRating
    });
  }

  // Update recording status
  recording.status = decision === 'approved' ? 'approved' : 
                   decision === 'rejected' ? 'rejected' : 'needs_revision';
  await recording.save();

  res.status(201).json(
    new ApiResponse(201, review, "Review created successfully")
  );
});

/**
 * Get reviews for an agency
 * @route GET /api/v1/voice/reviews/agency
 * @access Agency only
 */
export const getAgencyReviews = asyncHandler(async (req, res) => {
  if (req.user.role !== 'agency') {
    throw new ApiError(403, "Only agencies can view their reviews");
  }

  const { 
    decision, 
    scriptId, 
    modelId, 
    page = 1, 
    limit = 10, 
    sortBy = 'reviewedAt', 
    sortOrder = 'desc',
    startDate,
    endDate
  } = req.query;
  
  // Build query
  const query = {
    agencyId: req.user._id,
    isDeleted: false
  };

  if (decision) {
    const decisionArray = Array.isArray(decision) ? decision : decision.split(',');
    query.decision = { $in: decisionArray };
  }
  
  if (scriptId && mongoose.Types.ObjectId.isValid(scriptId)) {
    query.scriptId = scriptId;
  }
  
  if (modelId && mongoose.Types.ObjectId.isValid(modelId)) {
    query.modelId = modelId;
  }

  if (startDate && endDate) {
    query.reviewedAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }

  // Calculate pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);
  
  // Build sort object
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // Get reviews with populated data
  const reviews = await VoiceReview.find(query)
    .populate({
      path: 'scriptId',
      select: 'title category tags'
    })
    .populate({
      path: 'modelId',
      select: 'fullName username'
    })
    .populate({
      path: 'assignmentId',
      select: 'priority deadline status'
    })
    .populate({
      path: 'recordingId',
      select: 'originalFilename duration fileSize qualityScore'
    })
    .sort(sort)
    .skip(skip)
    .limit(parseInt(limit))
    .lean();

  // Get total count for pagination
  const totalReviews = await VoiceReview.countDocuments(query);
  
  // Add calculated fields
  const reviewsWithDetails = reviews.map(review => ({
    ...review,
    averageDetailedRating: review.averageDetailedRating,
    reviewCompletionTime: review.reviewCompletionTime,
    totalIssuesCount: review.totalIssuesCount
  }));

  res.status(200).json(
    new ApiResponse(200, {
      reviews: reviewsWithDetails,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalReviews / parseInt(limit)),
        totalReviews,
        hasNext: skip + reviews.length < totalReviews,
        hasPrev: parseInt(page) > 1
      }
    }, "Agency reviews retrieved successfully")
  );
});

/**
 * Get reviews for a model
 * @route GET /api/v1/voice/reviews/model
 * @access Model only
 */
export const getModelReviews = asyncHandler(async (req, res) => {
  if (req.user.role !== 'model') {
    throw new ApiError(403, "Only models can view their reviews");
  }

  const { 
    decision, 
    scriptId, 
    page = 1, 
    limit = 10, 
    sortBy = 'reviewedAt', 
    sortOrder = 'desc',
    startDate,
    endDate
  } = req.query;
  
  // Build query
  const query = {
    modelId: req.user._id,
    isDeleted: false
  };

  if (decision) {
    const decisionArray = Array.isArray(decision) ? decision : decision.split(',');
    query.decision = { $in: decisionArray };
  }
  
  if (scriptId && mongoose.Types.ObjectId.isValid(scriptId)) {
    query.scriptId = scriptId;
  }

  if (startDate && endDate) {
    query.reviewedAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }

  // Calculate pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);
  
  // Build sort object
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // Get reviews with populated data (excluding private notes)
  const reviews = await VoiceReview.find(query)
    .populate({
      path: 'scriptId',
      select: 'title category tags'
    })
    .populate({
      path: 'agencyId',
      select: 'agencyName'
    })
    .populate({
      path: 'assignmentId',
      select: 'priority deadline status'
    })
    .populate({
      path: 'recordingId',
      select: 'originalFilename duration fileSize version'
    })
    .select('-privateNotes') // Exclude private notes from model view
    .sort(sort)
    .skip(skip)
    .limit(parseInt(limit))
    .lean();

  // Get total count for pagination
  const totalReviews = await VoiceReview.countDocuments(query);
  
  // Add calculated fields
  const reviewsWithDetails = reviews.map(review => ({
    ...review,
    averageDetailedRating: review.averageDetailedRating,
    reviewCompletionTime: review.reviewCompletionTime,
    totalIssuesCount: review.totalIssuesCount
  }));

  res.status(200).json(
    new ApiResponse(200, {
      reviews: reviewsWithDetails,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalReviews / parseInt(limit)),
        totalReviews,
        hasNext: skip + reviews.length < totalReviews,
        hasPrev: parseInt(page) > 1
      }
    }, "Model reviews retrieved successfully")
  );
});

/**
 * Get review by ID
 * @route GET /api/v1/voice/reviews/:id
 * @access Agency (own reviews) and Model (own reviews, excluding private notes)
 */
export const getReviewById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid review ID");
  }

  let selectFields = '';
  if (req.user.role === 'model') {
    selectFields = '-privateNotes'; // Exclude private notes for models
  }

  const review = await VoiceReview.findById(id)
    .select(selectFields)
    .populate({
      path: 'scriptId',
      select: 'title description category tags'
    })
    .populate({
      path: 'modelId',
      select: 'fullName username'
    })
    .populate({
      path: 'agencyId',
      select: 'agencyName'
    })
    .populate({
      path: 'assignmentId',
      select: 'priority deadline status revisionCount'
    })
    .populate({
      path: 'recordingId',
      select: 'originalFilename duration fileSize qualityScore version'
    });

  if (!review || review.isDeleted) {
    throw new ApiError(404, "Review not found");
  }

  // Check access permissions
  if (req.user.role === 'model') {
    if (review.modelId._id.toString() !== req.user._id.toString()) {
      throw new ApiError(403, "Access denied");
    }
  } else if (req.user.role === 'agency') {
    if (review.agencyId._id.toString() !== req.user._id.toString()) {
      throw new ApiError(403, "Access denied");
    }
  } else {
    throw new ApiError(403, "Access denied");
  }

  res.status(200).json(
    new ApiResponse(200, {
      review,
      averageDetailedRating: review.averageDetailedRating,
      reviewCompletionTime: review.reviewCompletionTime,
      totalIssuesCount: review.totalIssuesCount,
      qualityScore: review.calculateQualityScore()
    }, "Review retrieved successfully")
  );
});

/**
 * Update review
 * @route PUT /api/v1/voice/reviews/:id
 * @access Agency only
 */
export const updateReview = asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid review ID");
  }

  if (req.user.role !== 'agency') {
    throw new ApiError(403, "Only agencies can update reviews");
  }

  const review = await VoiceReview.findById(id);
  
  if (!review || review.isDeleted) {
    throw new ApiError(404, "Review not found");
  }

  if (review.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  // Check if review can be updated (within 24 hours and not escalated)
  const hoursSinceReview = (new Date() - review.reviewedAt) / (1000 * 60 * 60);
  if (hoursSinceReview > 24) {
    throw new ApiError(400, "Cannot update review after 24 hours");
  }

  if (review.escalationStatus === 'escalated') {
    throw new ApiError(400, "Cannot update escalated review");
  }

  // Update allowed fields
  const allowedUpdates = [
    'decision', 'overallRating', 'detailedRatings', 'comments', 
    'privateNotes', 'positiveAspects', 'improvementAreas', 
    'suggestions', 'qualityFlags', 'revisionRequirements'
  ];
  
  const updates = {};
  allowedUpdates.forEach(field => {
    if (req.body.hasOwnProperty(field)) {
      updates[field] = req.body[field];
    }
  });

  // Add to revision history
  const revisionEntry = {
    updatedAt: new Date(),
    updatedBy: req.user._id,
    changes: Object.keys(updates),
    reason: req.body.updateReason || 'Review updated'
  };
  
  updates.$push = { revisionHistory: revisionEntry };
  updates.lastUpdatedAt = new Date();

  const updatedReview = await VoiceReview.findByIdAndUpdate(
    id,
    updates,
    { new: true, runValidators: true }
  );

  // Update assignment if decision changed
  if (req.body.decision && req.body.decision !== review.decision) {
    const assignment = await VoiceAssignment.findById(review.assignmentId);
    if (assignment) {
      if (req.body.decision === 'approved') {
        await assignment.approve(req.user._id, req.body.comments, req.body.overallRating);
      } else if (req.body.decision === 'rejected') {
        assignment.status = 'rejected';
        assignment.reviewComments = req.body.comments;
        assignment.rating = req.body.overallRating;
        await assignment.save();
      }
    }
  }

  res.status(200).json(
    new ApiResponse(200, updatedReview, "Review updated successfully")
  );
});

/**
 * Acknowledge review feedback (model)
 * @route POST /api/v1/voice/reviews/:id/acknowledge
 * @access Model only
 */
export const acknowledgeFeedback = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { response, acknowledgedAt } = req.body;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid review ID");
  }

  if (req.user.role !== 'model') {
    throw new ApiError(403, "Only models can acknowledge feedback");
  }

  const review = await VoiceReview.findById(id);
  
  if (!review || review.isDeleted) {
    throw new ApiError(404, "Review not found");
  }

  if (review.modelId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  // Acknowledge feedback
  await review.acknowledgeFeedback(response);

  res.status(200).json(
    new ApiResponse(200, review, "Feedback acknowledged successfully")
  );
});

/**
 * Escalate review (model)
 * @route POST /api/v1/voice/reviews/:id/escalate
 * @access Model only
 */
export const escalateReview = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { reason, description } = req.body;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid review ID");
  }

  if (req.user.role !== 'model') {
    throw new ApiError(403, "Only models can escalate reviews");
  }

  if (!reason) {
    throw new ApiError(400, "Escalation reason is required");
  }

  const review = await VoiceReview.findById(id);
  
  if (!review || review.isDeleted) {
    throw new ApiError(404, "Review not found");
  }

  if (review.modelId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  if (review.escalationStatus === 'escalated') {
    throw new ApiError(400, "Review is already escalated");
  }

  // Escalate review
  await review.escalate(reason, description);

  res.status(200).json(
    new ApiResponse(200, review, "Review escalated successfully")
  );
});

/**
 * Get pending reviews for agency
 * @route GET /api/v1/voice/reviews/pending
 * @access Agency only
 */
export const getPendingReviews = asyncHandler(async (req, res) => {
  if (req.user.role !== 'agency') {
    throw new ApiError(403, "Only agencies can view pending reviews");
  }

  const { page = 1, limit = 10, priority, scriptId } = req.query;
  
  // Build query for assignments that need review
  const assignmentQuery = {
    agencyId: req.user._id,
    status: 'submitted',
    isDeleted: false
  };

  if (priority) {
    assignmentQuery.priority = priority;
  }

  if (scriptId && mongoose.Types.ObjectId.isValid(scriptId)) {
    assignmentQuery.scriptId = scriptId;
  }

  // Calculate pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);

  // Get assignments that need review
  const assignments = await VoiceAssignment.find(assignmentQuery)
    .populate({
      path: 'scriptId',
      select: 'title category tags expectedDuration'
    })
    .populate({
      path: 'modelId',
      select: 'fullName username'
    })
    .sort({ priority: -1, completedAt: 1 }) // High priority first, then oldest first
    .skip(skip)
    .limit(parseInt(limit))
    .lean();

  // Get recordings for each assignment
  const assignmentsWithRecordings = await Promise.all(
    assignments.map(async (assignment) => {
      const recording = await VoiceRecording.findOne({
        assignmentId: assignment._id,
        isDeleted: false
      }).sort({ version: -1, uploadCompletedAt: -1 });
      
      return {
        ...assignment,
        recording,
        waitingTime: new Date() - new Date(assignment.completedAt)
      };
    })
  );

  // Get total count
  const totalPending = await VoiceAssignment.countDocuments(assignmentQuery);

  res.status(200).json(
    new ApiResponse(200, {
      pendingReviews: assignmentsWithRecordings,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalPending / parseInt(limit)),
        totalPending,
        hasNext: skip + assignments.length < totalPending,
        hasPrev: parseInt(page) > 1
      }
    }, "Pending reviews retrieved successfully")
  );
});

/**
 * Get review statistics
 * @route GET /api/v1/voice/reviews/stats
 * @access Agency and Model
 */
export const getReviewStats = asyncHandler(async (req, res) => {
  let matchStage = { isDeleted: false };
  
  if (req.user.role === 'model') {
    matchStage.modelId = req.user._id;
  } else if (req.user.role === 'agency') {
    matchStage.agencyId = req.user._id;
  } else {
    throw new ApiError(403, "Access denied");
  }

  const { startDate, endDate } = req.query;
  
  if (startDate && endDate) {
    matchStage.reviewedAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }

  // Get decision breakdown
  const decisionStats = await VoiceReview.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$decision',
        count: { $sum: 1 },
        avgRating: { $avg: '$overallRating' },
        avgReviewTime: { $avg: '$reviewCompletionTime' }
      }
    }
  ]);

  // Get quality flags breakdown
  const qualityStats = await VoiceReview.aggregate([
    { $match: matchStage },
    { $unwind: { path: '$qualityFlags', preserveNullAndEmptyArrays: true } },
    {
      $group: {
        _id: '$qualityFlags',
        count: { $sum: 1 }
      }
    }
  ]);

  // Get average ratings by category
  const categoryStats = await VoiceReview.aggregate([
    { $match: matchStage },
    {
      $lookup: {
        from: 'voicescripts',
        localField: 'scriptId',
        foreignField: '_id',
        as: 'script'
      }
    },
    { $unwind: '$script' },
    {
      $group: {
        _id: '$script.category',
        avgRating: { $avg: '$overallRating' },
        count: { $sum: 1 }
      }
    }
  ]);

  res.status(200).json(
    new ApiResponse(200, {
      decisionStats,
      qualityStats,
      categoryStats
    }, "Review statistics retrieved successfully")
  );
});