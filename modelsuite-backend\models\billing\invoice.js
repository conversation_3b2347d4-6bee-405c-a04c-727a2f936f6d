import mongoose from "mongoose";

const invoiceSchema = new mongoose.Schema(
  {
    client: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: true,
    },
    campaign: { type: String },
    amount: { type: Number, required: true },
    currency: { type: String, required: true },
    dueDate: { type: Date, required: true },
    status: {
      type: String,
      enum: ["Paid", "Unpaid", "Failed"],
      required: true,
    },
    note: { type: String },
    fileUrl: { type: String, required: true },
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: true,
    },

    // 🆕 ADD THESE FIELDS:
    transactionMethod: {
      type: String,
      enum: [
        "UPI",
        "Bank Transfer",
        "Cash",
        "SEPA",
        "Sofort",
        "PayPal",
        "Giropay",
        "Apple Pay",
        "Google Pay",
        "Credit Card",
      ],
    },
    screenshotUrl: { type: String }, // 📎 Store file path
  },
  { timestamps: true }
);
invoiceSchema.virtual("dueStatus").get(function () {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // normalize today

  const dueDate = new Date(this.dueDate);
  dueDate.setHours(0, 0, 0, 0);

  if (this.status === "Paid") {
    if (dueDate >= today) {
      return "Paid In Advance";
    } else {
      return "Paid Late";
    }
  }

  if (this.status === "Unpaid") {
    if (dueDate < today) {
      return "Overdue";
    } else {
      return "Upcoming";
    }
  }

  if (this.status === "Failed") {
    return "Payment Failed";
  }

  return "Unknown";
});

invoiceSchema.set("toJSON", { virtuals: true });

// invoiceSchema.set("toJSON", {
//   transform(doc, ret) {
//     const today = new Date();
//     today.setHours(0, 0, 0, 0); // remove time part for comparison

//     if (
//       ret.status === "Unpaid" &&
//       new Date(ret.dueDate).getTime() < today.getTime()
//     ) {
//       ret.status = "Overdue"; // ⚠️ only in output (not DB)
//     }

//     return ret;
//   },
// });
// ✅ Self-deleting logic: IIFE — runs immediately when file is imported

const Invoice = mongoose.model("Invoice", invoiceSchema);

export default Invoice;

// (async () => {
//   try {
//     const deleted = await Invoice.deleteMany({});
//     console.log(`[Invoice Cleanup] Deleted ${deleted.deletedCount} invoices.`);
//   } catch (error) {
//     console.error("[Invoice Cleanup Error]", error);
//   }
// })();
