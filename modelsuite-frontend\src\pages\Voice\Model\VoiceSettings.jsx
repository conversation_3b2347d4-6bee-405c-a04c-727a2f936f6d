import React, { useState, useEffect, useRef } from 'react';
import { Save, Mic, Volume2, Settings, TestTube, RefreshCw } from 'lucide-react';
import { toast } from 'react-hot-toast';

const VoiceSettings = () => {
  const [settings, setSettings] = useState({
    audioQuality: 'high',
    sampleRate: 44100,
    bitRate: 320,
    inputDevice: '',
    inputGain: 50,
    noiseReduction: true,
    autoGainControl: true,
    echoCancellation: true,
    recordingFormat: 'wav',
    maxRecordingLength: 300,
    autoSave: true,
    notifications: {
      newAssignments: true,
      reviewUpdates: true,
      deadlineReminders: true
    }
  });
  
  const [devices, setDevices] = useState([]);
  const [isTestingMic, setIsTestingMic] = useState(false);
  const [micLevel, setMicLevel] = useState(0);
  const [saving, setSaving] = useState(false);
  const [loading, setLoading] = useState(true);
  
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const streamRef = useRef(null);
  const animationFrameRef = useRef(null);

  useEffect(() => {
    loadSettings();
    loadAudioDevices();
    
    return () => {
      stopMicTest();
    };
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch('/api/v1/voice/settings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      if (data.success && data.data) {
        setSettings(prev => ({ ...prev, ...data.data }));
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadAudioDevices = async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioInputs = devices.filter(device => device.kind === 'audioinput');
      setDevices(audioInputs);
      
      // Set default device if none selected
      if (!settings.inputDevice && audioInputs.length > 0) {
        setSettings(prev => ({ ...prev, inputDevice: audioInputs[0].deviceId }));
      }
    } catch (error) {
      console.error('Failed to load audio devices:', error);
      toast.error('Failed to access audio devices');
    }
  };

  const startMicTest = async () => {
    try {
      const constraints = {
        audio: {
          deviceId: settings.inputDevice ? { exact: settings.inputDevice } : undefined,
          echoCancellation: settings.echoCancellation,
          noiseSuppression: settings.noiseReduction,
          autoGainControl: settings.autoGainControl
        }
      };
      
      streamRef.current = await navigator.mediaDevices.getUserMedia(constraints);
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      analyserRef.current = audioContextRef.current.createAnalyser();
      
      const source = audioContextRef.current.createMediaStreamSource(streamRef.current);
      source.connect(analyserRef.current);
      
      analyserRef.current.fftSize = 256;
      const bufferLength = analyserRef.current.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      
      setIsTestingMic(true);
      
      const updateMicLevel = () => {
        if (analyserRef.current) {
          analyserRef.current.getByteFrequencyData(dataArray);
          const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength;
          setMicLevel(Math.round((average / 255) * 100));
          animationFrameRef.current = requestAnimationFrame(updateMicLevel);
        }
      };
      
      updateMicLevel();
      toast.success('Microphone test started');
    } catch (error) {
      console.error('Failed to start mic test:', error);
      toast.error('Failed to access microphone');
    }
  };

  const stopMicTest = () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }
    if (audioContextRef.current) {
      audioContextRef.current.close();
    }
    setIsTestingMic(false);
    setMicLevel(0);
  };

  const handleSettingChange = (key, value) => {
    if (key.includes('.')) {
      const [parent, child] = key.split('.');
      setSettings(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setSettings(prev => ({ ...prev, [key]: value }));
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      const response = await fetch('/api/v1/voice/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(settings)
      });
      
      const data = await response.json();
      if (data.success) {
        toast.success('Settings saved successfully');
      } else {
        toast.error(data.message || 'Failed to save settings');
      }
    } catch (error) {
      toast.error('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = () => {
    setSettings({
      audioQuality: 'high',
      sampleRate: 44100,
      bitRate: 320,
      inputDevice: devices.length > 0 ? devices[0].deviceId : '',
      inputGain: 50,
      noiseReduction: true,
      autoGainControl: true,
      echoCancellation: true,
      recordingFormat: 'wav',
      maxRecordingLength: 300,
      autoSave: true,
      notifications: {
        newAssignments: true,
        reviewUpdates: true,
        deadlineReminders: true
      }
    });
    toast.success('Settings reset to defaults');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Voice Settings</h1>
              <p className="text-gray-600 mt-1">Configure your recording preferences and audio settings</p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={resetToDefaults}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                Reset
              </button>
              <button
                onClick={saveSettings}
                disabled={saving}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50"
              >
                <Save className="w-4 h-4" />
                {saving ? 'Saving...' : 'Save Settings'}
              </button>
            </div>
          </div>
        </div>

        <div className="p-6 space-y-8">
          {/* Audio Quality Settings */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Volume2 className="w-5 h-5" />
              Audio Quality
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quality Preset
                </label>
                <select
                  value={settings.audioQuality}
                  onChange={(e) => handleSettingChange('audioQuality', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="low">Low (Good for drafts)</option>
                  <option value="medium">Medium (Balanced)</option>
                  <option value="high">High (Best quality)</option>
                  <option value="custom">Custom</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Recording Format
                </label>
                <select
                  value={settings.recordingFormat}
                  onChange={(e) => handleSettingChange('recordingFormat', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="wav">WAV (Uncompressed)</option>
                  <option value="mp3">MP3 (Compressed)</option>
                  <option value="m4a">M4A (Apple)</option>
                </select>
              </div>
              
              {settings.audioQuality === 'custom' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Sample Rate: {settings.sampleRate} Hz
                    </label>
                    <select
                      value={settings.sampleRate}
                      onChange={(e) => handleSettingChange('sampleRate', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value={22050}>22.05 kHz</option>
                      <option value={44100}>44.1 kHz</option>
                      <option value={48000}>48 kHz</option>
                      <option value={96000}>96 kHz</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Bit Rate: {settings.bitRate} kbps
                    </label>
                    <select
                      value={settings.bitRate}
                      onChange={(e) => handleSettingChange('bitRate', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value={128}>128 kbps</option>
                      <option value={192}>192 kbps</option>
                      <option value={256}>256 kbps</option>
                      <option value={320}>320 kbps</option>
                    </select>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Microphone Settings */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Mic className="w-5 h-5" />
              Microphone Settings
            </h2>
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Input Device
                </label>
                <div className="flex gap-3">
                  <select
                    value={settings.inputDevice}
                    onChange={(e) => handleSettingChange('inputDevice', e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {devices.map(device => (
                      <option key={device.deviceId} value={device.deviceId}>
                        {device.label || `Microphone ${device.deviceId.slice(0, 8)}`}
                      </option>
                    ))}
                  </select>
                  <button
                    onClick={loadAudioDevices}
                    className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <RefreshCw className="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Input Gain: {settings.inputGain}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={settings.inputGain}
                  onChange={(e) => handleSettingChange('inputGain', parseInt(e.target.value))}
                  className="w-full"
                />
              </div>
              
              {/* Microphone Test */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-gray-700">Microphone Test</span>
                  <button
                    onClick={isTestingMic ? stopMicTest : startMicTest}
                    className={`flex items-center gap-2 px-3 py-1 rounded text-sm transition-colors ${
                      isTestingMic 
                        ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                        : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                    }`}
                  >
                    <TestTube className="w-4 h-4" />
                    {isTestingMic ? 'Stop Test' : 'Test Mic'}
                  </button>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all ${
                      micLevel > 70 ? 'bg-red-500' : micLevel > 40 ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${micLevel}%` }}
                  />
                </div>
                <div className="text-xs text-gray-600 mt-1">
                  Level: {micLevel}% {isTestingMic && '(Speak into your microphone)'}
                </div>
              </div>
            </div>
          </div>

          {/* Audio Processing */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Audio Processing
            </h2>
            <div className="space-y-4">
              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={settings.noiseReduction}
                  onChange={(e) => handleSettingChange('noiseReduction', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium text-gray-700">Noise Reduction</span>
                  <p className="text-xs text-gray-500">Reduces background noise during recording</p>
                </div>
              </label>
              
              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={settings.autoGainControl}
                  onChange={(e) => handleSettingChange('autoGainControl', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium text-gray-700">Auto Gain Control</span>
                  <p className="text-xs text-gray-500">Automatically adjusts microphone sensitivity</p>
                </div>
              </label>
              
              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={settings.echoCancellation}
                  onChange={(e) => handleSettingChange('echoCancellation', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium text-gray-700">Echo Cancellation</span>
                  <p className="text-xs text-gray-500">Removes echo and feedback</p>
                </div>
              </label>
            </div>
          </div>

          {/* Recording Preferences */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Recording Preferences</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maximum Recording Length: {Math.floor(settings.maxRecordingLength / 60)}:{(settings.maxRecordingLength % 60).toString().padStart(2, '0')}
                </label>
                <input
                  type="range"
                  min="60"
                  max="1800"
                  step="30"
                  value={settings.maxRecordingLength}
                  onChange={(e) => handleSettingChange('maxRecordingLength', parseInt(e.target.value))}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>1 min</span>
                  <span>30 min</span>
                </div>
              </div>
              
              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={settings.autoSave}
                  onChange={(e) => handleSettingChange('autoSave', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <div>
                  <span className="text-sm font-medium text-gray-700">Auto-save recordings</span>
                  <p className="text-xs text-gray-500">Automatically save recordings as drafts</p>
                </div>
              </label>
            </div>
          </div>

          {/* Notifications */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Notifications</h2>
            <div className="space-y-4">
              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={settings.notifications.newAssignments}
                  onChange={(e) => handleSettingChange('notifications.newAssignments', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700">New assignment notifications</span>
              </label>
              
              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={settings.notifications.reviewUpdates}
                  onChange={(e) => handleSettingChange('notifications.reviewUpdates', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700">Review update notifications</span>
              </label>
              
              <label className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={settings.notifications.deadlineReminders}
                  onChange={(e) => handleSettingChange('notifications.deadlineReminders', e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700">Deadline reminder notifications</span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VoiceSettings;