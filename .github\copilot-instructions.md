# ModelSuite AI Agent Instructions

## Architecture at a glance

- **Backend**: Express.js API (`server.js`), controllers in `config/controllers/`, models in `models/`, routes in `routes/`, real-time handlers in `sockets/`.
- **Frontend**: React 19 + Vite (`modelsuite-frontend/`); pages in `src/pages`; components in `src/components`; Tailwind CSS & PostCSS.
- **Database**: MongoDB via Mongoose; connection in `db.js`.
- **Auth**: JWT in `middlewares/authMiddleware.js`, role checks in `middlewares/roleCheck.js`.
- **Real-time Messaging**: Client in `utils/socket.js`, server in `sockets/index.js` and `sockets/messanger/`.

## Key developer workflows

```powershell
# Backend (port 3000)
cd modelsuite-backend; npm install; npm run dev; npm test

# Frontend (port 4000)
cd ../modelsuite-frontend; npm install; npm run dev; npm run lint
```

## File structure patterns

**Backend**:

- Controllers in `config/controllers/{feature}`.
- Models in `models/{feature}`.
- Routes in `routes/{feature}Routes.js` mounted at `/api/v1/{feature}`.
- Utilities in `utils/` (e.g., `asyncHandler.js`, `ApiError.js`, `tikapiClient.js`, `seedDefaultTemplate.js`).
- Middlewares in `middlewares/` (auth, roles, file uploads).

**Frontend**:

- Pages under `src/pages/Model` and `src/pages/Agency`, wrapped by layouts in `src/layouts`.
- Shared UI in `src/components/ui`; feature components in `src/components/{feature}`.
- State via Redux slices in `src/globalstate/{channel,dm,group}Slice.js`; context in `src/context`.
- API client in `src/utils/questionnaireApi.js` with JWT interceptor.

## Conventions & patterns

- Wrap async controllers with `asyncHandler`; throw `new ApiError(status, message)`.
- Guard by `req.user.role` in backend and `<ProtectedRoute allowedRole="...">` in frontend.
- Socket registration: `socket.emit('register', {userId})`; server emits via `req.app.get('io').emit()`.
- File uploads: Multer + Cloudinary via `middlewares/cloudinaryUpload.js`.
- Default templates seeded at startup (`utils/seedDefaultTemplate.js`).

## Integration points

- **Social Media**: TikTok (`utils/tikapiClient.js`); Instagram/Facebook in `config/controllers/socialMedia/`.
- **Contract Management**: PandaDoc via `config/controllers/pandadoccontroller.js`.
- **Google Calendar**: Client in `utils/googleCalendar.js`; server in `config/controllers/google/eventController.js`.
- **Billing**: Handlers in `config/controllers/billing/`; schemas in `models/billing/`.

## Notes for AI agents

- Follow existing ES6 style and file naming conventions.
- Return error responses as `{ error: 'message' }` JSON.
- Prefix new endpoints under `/api/v1`.
- Reuse utilities (`asyncHandler`, `ApiResponse`).
- Confirm cross-feature real-time flows before implementing changes.

_Feedback welcome on any unclear or missing areas._
