import express from "express";
import multer from "multer";
import { v2 as cloudinary } from "cloudinary";
import { Readable } from "stream";

const router = express.Router();

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Configure multer for memory storage
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow all file types for now
    cb(null, true);
  },
});

// Helper function to determine resource type for Cloudinary
const getCloudinaryResourceType = (mimeType) => {
  if (mimeType.startsWith("image/")) return "image";
  if (mimeType.startsWith("video/")) return "video";
  if (mimeType.startsWith("audio/")) return "audio";
  return "raw"; // For documents, archives, etc.
};

// Helper function to get file type category
const getFileTypeCategory = (mimeType, filename) => {
  const extension = filename.split(".").pop().toLowerCase();

  if (mimeType.startsWith("image/")) return "image";
  if (mimeType.startsWith("video/")) return "video";
  if (mimeType.startsWith("audio/")) return "audio";

  // Archives
  if (
    ["zip", "rar", "7z", "tar", "gz"].includes(extension) ||
    mimeType.includes("zip") ||
    mimeType.includes("archive")
  ) {
    return "archive";
  }

  // PDFs
  if (extension === "pdf" || mimeType === "application/pdf") return "pdf";

  // Spreadsheets
  if (
    ["xlsx", "xls", "csv"].includes(extension) ||
    mimeType.includes("spreadsheet")
  ) {
    return "spreadsheet";
  }

  // Code files
  if (
    [
      "js",
      "jsx",
      "ts",
      "tsx",
      "html",
      "css",
      "json",
      "xml",
      "py",
      "java",
      "cpp",
      "c",
    ].includes(extension)
  ) {
    return "code";
  }

  // Documents
  if (
    ["doc", "docx", "txt", "rtf"].includes(extension) ||
    mimeType.includes("document") ||
    mimeType.includes("text")
  ) {
    return "document";
  }

  return "file";
};

// Helper function to create folder structure
const generateCloudinaryFolder = (userId, chatType = "dm") => {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  return `chat-app/${chatType}/${userId}/${year}/${month}`;
};

// Helper function to generate secure filename
const generateSecureFilename = (originalName, fileId) => {
  const extension = originalName.split(".").pop();
  const timestamp = Date.now();
  return `${fileId}_${timestamp}.${extension}`;
};

// Helper function to upload single file to Cloudinary
const uploadToCloudinary = (buffer, options) => {
  return new Promise((resolve, reject) => {
    const stream = cloudinary.uploader.upload_stream(
      options,
      (error, result) => {
        if (error) {
          console.error("Cloudinary upload error:", error);
          reject(error);
        } else {
          resolve(result);
        }
      },
    );

    // Create readable stream from buffer
    const readableStream = new Readable();
    readableStream.push(buffer);
    readableStream.push(null);
    readableStream.pipe(stream);
  });
};

// Helper function to generate video thumbnail
const generateVideoThumbnail = async (publicId) => {
  try {
    const thumbnailUrl = cloudinary.url(publicId, {
      resource_type: "video",
      format: "jpg",
      transformation: [
        { width: 300, height: 200, crop: "fill", quality: "auto" },
        { flags: "attachment" },
      ],
    });
    return thumbnailUrl;
  } catch (error) {
    console.error("Error generating video thumbnail:", error);
    return null;
  }
};

// Main upload endpoint
router.post("/upload", upload.array("files"), async (req, res) => {
  try {
    const files = req.files;
    const fileIds = req.body.fileIds;
    const messageId = req.headers["message-id"];
    const convoId = req.body.convoId;
    const userId = req.user?.id || req.user?._id; // Adjust based on your auth middleware

    if (!files || files.length === 0) {
      return res.status(400).json({ error: "No files provided" });
    }

    if (!userId) {
      return res.status(401).json({ error: "User not authenticated" });
    }

    // Validate file IDs
    const fileIdArray = Array.isArray(fileIds) ? fileIds : [fileIds];
    if (fileIdArray.length !== files.length) {
      return res
        .status(400)
        .json({ error: "File IDs count does not match files count" });
    }

    // Set headers for streaming response
    res.writeHead(200, {
      "Content-Type": "application/json",
      "Transfer-Encoding": "chunked",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
      "Access-Control-Allow-Origin": "*",
    });

    const uploadResults = [];
    const folder = generateCloudinaryFolder(userId);

    // Send initial response
    res.write(
      JSON.stringify({
        type: "start",
        totalFiles: files.length,
        messageId,
      }) + "\n",
    );

    // Process each file
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileId = fileIdArray[i];

      try {
        // Send progress start
        res.write(
          JSON.stringify({
            type: "progress",
            fileId,
            progress: 0,
            fileName: file.originalname,
            status: "uploading",
          }) + "\n",
        );

        // Determine resource type and file category
        const resourceType = getCloudinaryResourceType(file.mimetype);
        const fileCategory = getFileTypeCategory(
          file.mimetype,
          file.originalname,
        );
        const secureFilename = generateSecureFilename(
          file.originalname,
          fileId,
        );

        // Prepare upload options
        const uploadOptions = {
          folder,
          resource_type: resourceType,
          public_id: secureFilename.split(".")[0], // Remove extension from public_id
          use_filename: false,
          unique_filename: true,
          overwrite: false,
          context: {
            fileId,
            messageId: messageId || "",
            convoId: convoId || "",
            originalName: file.originalname,
            fileCategory,
            uploadedBy: userId,
          },
          // Add transformation for images to optimize size
          ...(resourceType === "image" && {
            transformation: [{ quality: "auto:good" }, { format: "auto" }],
          }),
          // Add video-specific options
          ...(resourceType === "video" && {
            video_codec: "auto",
            quality: "auto:good",
          }),
          // Add audio-specific options
          ...(resourceType === "audio" && {
            audio_codec: "auto",
          }),
        };

        // Send progress update
        res.write(
          JSON.stringify({
            type: "progress",
            fileId,
            progress: 25,
            fileName: file.originalname,
            status: "processing",
          }) + "\n",
        );

        // Upload to Cloudinary
        const result = await uploadToCloudinary(file.buffer, uploadOptions);

        // Send progress update
        res.write(
          JSON.stringify({
            type: "progress",
            fileId,
            progress: 75,
            fileName: file.originalname,
            status: "finalizing",
          }) + "\n",
        );

        // Generate thumbnail for videos
        let thumbnailUrl = null;
        if (resourceType === "video") {
          thumbnailUrl = await generateVideoThumbnail(result.public_id);
        }

        // Prepare final result
        const uploadResult = {
          fileId,
          success: true,
          cloudinaryId: result.public_id,
          url: result.secure_url,
          thumbnailUrl,
          originalName: file.originalname,
          mimeType: file.mimetype,
          size: result.bytes,
          width: result.width || null,
          height: result.height || null,
          duration: result.duration || null,
          format: result.format,
          resourceType,
          fileCategory,
          createdAt: result.created_at,
        };

        uploadResults.push(uploadResult);

        // Send completion for this file
        res.write(
          JSON.stringify({
            type: "complete",
            fileId,
            progress: 100,
            fileName: file.originalname,
            status: "completed",
            result: uploadResult,
          }) + "\n",
        );

        console.log(`✅ uploaded a File: ${fileId}`);
      } catch (error) {
        console.error(`Upload failed for file ${fileId}:`, error);

        // Send error for this file
        res.write(
          JSON.stringify({
            type: "error",
            fileId,
            fileName: file.originalname,
            error: error.message || "Upload failed",
            status: "failed",
          }) + "\n",
        );

        uploadResults.push({
          fileId,
          success: false,
          error: error.message || "Upload failed",
          originalName: file.originalname,
        });
      }
    }

    // Send final response
    res.write(
      JSON.stringify({
        type: "finished",
        messageId,
        totalFiles: files.length,
        successCount: uploadResults.filter((r) => r.success).length,
        failureCount: uploadResults.filter((r) => !r.success).length,
        results: uploadResults,
      }) + "\n",
    );

    res.end();
  } catch (error) {
    console.error("Upload endpoint error:", error);

    // If headers haven't been sent yet, send error response
    if (!res.headersSent) {
      return res.status(500).json({
        error: "Internal server error",
        message: error.message,
      });
    } else {
      // If streaming has started, send error in stream format
      res.write(
        JSON.stringify({
          type: "error",
          error: "Internal server error",
          message: error.message,
        }) + "\n",
      );
      res.end();
    }
  }
});

// Endpoint to delete uploaded files (cleanup on message delete)
router.delete("/delete", async (req, res) => {
  try {
    const { cloudinaryIds } = req.body;
    const userId = req.user?.id || req.user?._id;

    if (!userId) {
      return res.status(401).json({ error: "User not authenticated" });
    }

    if (!cloudinaryIds || !Array.isArray(cloudinaryIds)) {
      return res.status(400).json({ error: "Invalid cloudinary IDs provided" });
    }

    const deleteResults = [];

    for (const cloudinaryId of cloudinaryIds) {
      try {
        const result = await cloudinary.uploader.destroy(cloudinaryId, {
          resource_type: "auto",
        });

        deleteResults.push({
          cloudinaryId,
          success: result.result === "ok",
          result: result.result,
        });
      } catch (error) {
        deleteResults.push({
          cloudinaryId,
          success: false,
          error: error.message,
        });
      }
    }

    res.json({
      success: true,
      results: deleteResults,
      deletedCount: deleteResults.filter((r) => r.success).length,
    });
  } catch (error) {
    console.error("Delete endpoint error:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error.message,
    });
  }
});

// Health check endpoint
router.get("/health", (req, res) => {
  res.json({
    status: "OK",
    service: "File Upload Service",
    timestamp: new Date().toISOString(),
  });
});

export default router;
