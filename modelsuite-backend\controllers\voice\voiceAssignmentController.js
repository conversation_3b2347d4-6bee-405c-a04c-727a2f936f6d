import VoiceAssignment from "../../models/voice/VoiceAssignment.js";
import QuestionTemplate from "../../models/voice/QuestionTemplate.js";
import QuestionSection from "../../models/voice/QuestionSection.js";
import VoiceRecording from "../../models/voice/VoiceRecording.js";
import ModelUser from "../../models/model.js";
import Agency from "../../models/agency.js";
import { asyncHandler } from "../../utils/asyncHandler.js";
import { ApiError } from "../../utils/ApiError.js";
import { ApiResponse } from "../../utils/ApiResponse.js";
import mongoose from "mongoose";

/**
 * Assign questions to models
 * @route POST /api/v1/voice/assignments
 * @access Agency only
 */
export const assignQuestions = asyncHandler(async (req, res) => {
  const { questionIds, modelIds, title, description, deadline, priority = 'medium' } = req.body;

  console.log(`🔄 Creating assignment with:`, {
    questionIds: questionIds?.length,
    modelIds: modelIds?.length,
    title,
    description,
    deadline,
    priority,
    agencyId: req.user._id
  });

  // Validate agency access
  if (req.user.role !== 'agency') {
    throw new ApiError(403, "Only agencies can assign questions");
  }

  // Validate required fields
  if (!questionIds || !Array.isArray(questionIds) || questionIds.length === 0) {
    throw new ApiError(400, "At least one question ID is required");
  }

  if (!modelIds || !Array.isArray(modelIds) || modelIds.length === 0) {
    throw new ApiError(400, "At least one model ID is required");
  }

  if (!title?.trim()) {
    throw new ApiError(400, "Assignment title is required");
  }

  // Validate deadline
  if (deadline && new Date(deadline) <= new Date()) {
    throw new ApiError(400, "Deadline must be in the future");
  }

  // Validate ObjectIds
  const invalidQuestionIds = questionIds.filter(id => !mongoose.Types.ObjectId.isValid(id));
  if (invalidQuestionIds.length > 0) {
    throw new ApiError(400, `Invalid question IDs: ${invalidQuestionIds.join(', ')}`);
  }

  const invalidModelIds = modelIds.filter(id => !mongoose.Types.ObjectId.isValid(id));
  if (invalidModelIds.length > 0) {
    throw new ApiError(400, `Invalid model IDs: ${invalidModelIds.join(', ')}`);
  }

  // Validate questions exist and are accessible
  const questions = await QuestionTemplate.find({ 
    _id: { $in: questionIds },
    isDeleted: false
  }).populate('sectionId');

  if (questions.length !== questionIds.length) {
    const foundIds = questions.map(q => q._id.toString());
    const notFoundIds = questionIds.filter(id => !foundIds.includes(id));
    throw new ApiError(404, `Questions not found: ${notFoundIds.join(', ')}`);
  }

  // Check access to questions (default questions or agency's own questions)
  const inaccessibleQuestions = questions.filter(q => {
    if (!q.sectionId) return true; // No section found
    return !q.sectionId.isDefault && q.sectionId.createdBy?.toString() !== req.user._id.toString();
  });

  if (inaccessibleQuestions.length > 0) {
    throw new ApiError(403, "Access denied to some questions");
  }

  // Validate models exist
  const models = await ModelUser.find({ 
    _id: { $in: modelIds },
    isDeleted: { $ne: true }
  });

  if (models.length !== modelIds.length) {
    const foundIds = models.map(m => m._id.toString());
    const notFoundIds = modelIds.filter(id => !foundIds.includes(id));
    throw new ApiError(404, `Models not found: ${notFoundIds.join(', ')}`);
  }

  // Create assignments with duplicate check
  const assignments = [];
  const errors = [];
  
  for (const modelId of modelIds) {
    try {
      // Find the model to get username for unique title
      const model = models.find(m => m._id.toString() === modelId);
      const uniqueTitle = `${title.trim()} - ${model.username}`;
      
      // Check if assignment already exists
      const existingAssignment = await VoiceAssignment.findOne({
        agencyId: req.user._id,
        modelId,
        title: uniqueTitle,
        isDeleted: false
      });
      
      console.log(`🔍 Checking for existing assignment:`, {
        agencyId: req.user._id,
        modelId,
        title: uniqueTitle,
        existing: !!existingAssignment
      });
      
      if (existingAssignment) {
        console.log(`❌ Assignment already exists:`, existingAssignment);
        errors.push({
          modelId,
          modelUsername: model.username,
          error: `Assignment already exists for model ${model.username} with title "${uniqueTitle}"`
        });
        continue;
      }
      
      // Create new assignment
      const assignment = await VoiceAssignment.create({
        questionIds,
        title: uniqueTitle,
        description: description?.trim() || '',
        modelId,
        agencyId: req.user._id,
        assignedBy: req.user._id,
        status: 'assigned',
        priority,
        deadline: deadline ? new Date(deadline) : null,
        assignedAt: new Date()
      });

      const populatedAssignment = await VoiceAssignment.findById(assignment._id)
        .populate({
          path: 'questionIds',
          select: 'text sectionId',
          populate: {
            path: 'sectionId',
            select: 'title description'
          }
        })
        .populate('modelId', 'username firstName lastName')
        .lean();
        
      assignments.push(populatedAssignment);
      
    } catch (err) {
      console.log(`❌ Error creating assignment for model ${modelId}:`, {
        name: err.name,
        message: err.message,
        code: err.code,
        stack: err.stack.split('\n').slice(0, 3).join('\n')
      });
      
      // Handle MongoDB duplicate key error
      if (err.code === 11000 || err.name === 'MongoError' || err.message.includes('duplicate')) {
        const model = models.find(m => m._id.toString() === modelId);
        errors.push({
          modelId,
          modelUsername: model?.username || 'Unknown',
          error: `Duplicate assignment: a record already exists with this agency, model, and title`
        });
      } else {
        // Re-throw other errors
        throw err;
      }
    }
  }
  
  // If no assignments were created successfully, return error
  if (assignments.length === 0 && errors.length > 0) {
    console.log(`❌ No assignments created. Errors:`, errors);
    throw new ApiError(409, "No assignments could be created", errors);
  }

  // Prepare response message
  let message = "Questions assigned successfully";
  let statusCode = 201;
  
  if (errors.length > 0) {
    if (assignments.length > 0) {
      message = `${assignments.length} assignments created successfully, ${errors.length} failed due to duplicates`;
      statusCode = 207; // Multi-status
    } else {
      message = "All assignments failed due to duplicates";
      statusCode = 409; // Conflict
    }
  }
  
  res.status(statusCode).json(
    new ApiResponse(statusCode, {
      assignments,
      assignedCount: assignments.length,
      assignmentTitle: title,
      questionsCount: questionIds.length,
      errors: errors.length > 0 ? errors : undefined,
      totalRequested: modelIds.length,
      successCount: assignments.length,
      errorCount: errors.length
    }, message)
  );
});

/**
 * Get assignments for a model
 * @route GET /api/v1/voice/my-assignments
 * @route GET /api/v1/voice/assignments/model
 * @access Model only
 */
export const getModelAssignments = asyncHandler(async (req, res) => {
  if (req.user.role !== 'model') {
    throw new ApiError(403, "Only models can view their assignments");
  }

  const { status, tags, page = 1, limit = 10, sortBy = 'assignedAt', sortOrder = 'desc' } = req.query;

  // Build query
  const query = {
    modelId: req.user._id,
    isDeleted: false
  };

  if (status && status !== 'all') {
    const statusArray = Array.isArray(status) ? status : status.split(',');
    query.status = { $in: statusArray };
  }

  // Add tag filtering - if tags are specified, find assignments that have questions with those tags
  let tagFilter = null;
  if (tags && tags !== 'all') {
    const tagArray = Array.isArray(tags) ? tags : tags.split(',');
    tagFilter = tagArray;
  }

  // Calculate pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);
  
  // Build sort object
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // Get assignments with populated data
  const assignments = await VoiceAssignment.find(query)
    .populate({
      path: 'questionIds',
      select: 'text sectionId tags',
      populate: {
        path: 'sectionId',
        select: 'title description'
      }
    })
    .populate({
      path: 'agencyId',
      select: 'agencyName'
    })
    .sort(sort)
    .skip(skip)
    .limit(parseInt(limit))
    .lean();

  // Apply tag filtering after population
  let filteredAssignments = assignments;
  if (tagFilter && tagFilter.length > 0) {
    filteredAssignments = assignments.filter(assignment => {
      if (!assignment.questionIds || assignment.questionIds.length === 0) return false;

      // Check if any question in the assignment has any of the specified tags
      return assignment.questionIds.some(question => {
        if (!question.tags || question.tags.length === 0) return false;
        return tagFilter.some(tag => question.tags.includes(tag));
      });
    });
  }

  // Get total count for pagination (before tag filtering for now - could be optimized)
  const totalAssignments = await VoiceAssignment.countDocuments(query);

  // Add recording info if exists
  const assignmentsWithRecordings = await Promise.all(
    filteredAssignments.map(async (assignment) => {
      const recordingsCount = assignment.recordings ? assignment.recordings.length : 0;
      const submittedRecordings = assignment.recordings ?
        assignment.recordings.filter(r => r.status === 'submitted').length : 0;

      return {
        ...assignment,
        recordingsInfo: {
          total: recordingsCount,
          submitted: submittedRecordings,
          pending: recordingsCount - submittedRecordings,
          questionsCount: assignment.questionIds ? assignment.questionIds.length : 0
        },
        timeRemaining: assignment.deadline ?
          Math.max(0, new Date(assignment.deadline) - new Date()) : null,
        isOverdue: assignment.deadline ? new Date() > new Date(assignment.deadline) : false
      };
    })
  );

  res.status(200).json(
    new ApiResponse(200, {
      assignments: assignmentsWithRecordings,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil((tagFilter ? assignmentsWithRecordings.length : totalAssignments) / parseInt(limit)),
        totalAssignments: tagFilter ? assignmentsWithRecordings.length : totalAssignments,
        hasNext: skip + assignmentsWithRecordings.length < (tagFilter ? assignmentsWithRecordings.length : totalAssignments),
        hasPrev: parseInt(page) > 1
      }
    }, "Model assignments retrieved successfully")
  );
});

/**
 * Get assignments for an agency
 * @route GET /api/v1/voice/assignments/agency
 * @access Agency only
 */
export const getAgencyAssignments = asyncHandler(async (req, res) => {
  if (req.user.role !== 'agency') {
    throw new ApiError(403, "Only agencies can view their assignments");
  }

  const { 
    status, 
    scriptId, 
    modelId, 
    page = 1, 
    limit = 10, 
    sortBy = 'assignedAt', 
    sortOrder = 'desc',
    search
  } = req.query;
  
  // Build query
  const query = {
    agencyId: req.user._id,
    isDeleted: false
  };

  if (status) {
    const statusArray = Array.isArray(status) ? status : status.split(',');
    query.status = { $in: statusArray };
  }
  
  if (modelId && mongoose.Types.ObjectId.isValid(modelId)) {
    query.modelId = modelId;
  }

  // Search by assignment title if provided
  if (search) {
    query.title = { $regex: search, $options: 'i' };
  }

  // Calculate pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);
  
  // Build sort object
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // Get assignments with populated data
  const assignments = await VoiceAssignment.find(query)
    .populate({
      path: 'questionIds',
      select: 'text sectionId tags',
      populate: {
        path: 'sectionId',
        select: 'title description'
      }
    })
    .populate('modelId', 'username firstName lastName fullName')
    .sort(sort)
    .skip(skip)
    .limit(parseInt(limit))
    .lean();

  // Get total count for pagination
  const totalAssignments = await VoiceAssignment.countDocuments(query);

  // Add recording info and time calculations
  const assignmentsWithDetails = await Promise.all(
    assignments.map(async (assignment) => {
      // Get recordings count from VoiceRecording collection
      const recordingsCount = await VoiceRecording.countDocuments({
        assignmentId: assignment._id,
        isDeleted: false
      });

      const submittedRecordings = await VoiceRecording.countDocuments({
        assignmentId: assignment._id,
        isDeleted: false,
        status: 'submitted'
      });

      return {
        ...assignment,
        recordingsInfo: {
          total: recordingsCount,
          submitted: submittedRecordings,
          pending: recordingsCount - submittedRecordings,
          questionsCount: assignment.questionIds ? assignment.questionIds.length : 0
        },
        timeRemaining: assignment.deadline ?
          Math.max(0, new Date(assignment.deadline) - new Date()) : null,
        isOverdue: assignment.deadline ? new Date() > new Date(assignment.deadline) : false,
        completionTime: assignment.completedAt && assignment.startedAt ?
          new Date(assignment.completedAt) - new Date(assignment.startedAt) : null
      };
    })
  );

  res.status(200).json(
    new ApiResponse(200, {
      assignments: assignmentsWithDetails,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(totalAssignments / parseInt(limit)),
        totalAssignments,
        hasNext: skip + assignments.length < totalAssignments,
        hasPrev: parseInt(page) > 1
      }
    }, "Agency assignments retrieved successfully")
  );
});

/**
 * Get single assignment details
 * @route GET /api/v1/voice/assignments/:id
 * @access Model (own assignments) and Agency (own assignments)
 */
export const getAssignmentById = asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid assignment ID");
  }

  const assignment = await VoiceAssignment.findById(id)
    .populate({
      path: 'questionIds',
      select: 'text sectionId',
      populate: {
        path: 'sectionId',
        select: 'title description'
      }
    })
    .populate({
      path: 'modelId',
      select: 'fullName username'
    })
    .populate({
      path: 'agencyId',
      select: 'agencyName'
    })
    .populate({
      path: 'assignedBy',
      select: 'agencyName'
    });

  if (!assignment || assignment.isDeleted) {
    throw new ApiError(404, "Assignment not found");
  }

  // Check access permissions
  if (req.user.role === 'model') {
    if (assignment.modelId._id.toString() !== req.user._id.toString()) {
      throw new ApiError(403, "Access denied");
    }
  } else if (req.user.role === 'agency') {
    if (assignment.agencyId._id.toString() !== req.user._id.toString()) {
      throw new ApiError(403, "Access denied");
    }
  } else {
    throw new ApiError(403, "Access denied");
  }

  // Get recordings for this assignment
  const recordings = assignment.recordings || [];
  const recordingsInfo = {
    total: recordings.length,
    submitted: recordings.filter(r => r.status === 'submitted').length,
    approved: recordings.filter(r => r.status === 'approved').length,
    rejected: recordings.filter(r => r.status === 'rejected').length,
    questionsCount: assignment.questionIds ? assignment.questionIds.length : 0
  };

  // Calculate time-related fields
  const timeRemaining = assignment.deadline ? 
    Math.max(0, new Date(assignment.deadline) - new Date()) : null;
  const isOverdue = assignment.deadline ? new Date() > new Date(assignment.deadline) : false;
  const completionTime = assignment.completedAt && assignment.startedAt ?
    new Date(assignment.completedAt) - new Date(assignment.startedAt) : null;

  res.status(200).json(
    new ApiResponse(200, {
      assignment,
      recordingsInfo,
      timeRemaining,
      isOverdue,
      completionTime
    }, "Assignment retrieved successfully")
  );
});

/**
 * Start working on an assignment (model)
 * @route POST /api/v1/voice/assignments/:id/start
 * @access Model only
 */
export const startAssignment = asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid assignment ID");
  }

  if (req.user.role !== 'model') {
    throw new ApiError(403, "Only models can start assignments");
  }

  const assignment = await VoiceAssignment.findById(id);
  
  if (!assignment || assignment.isDeleted) {
    throw new ApiError(404, "Assignment not found");
  }

  if (assignment.modelId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  if (assignment.status !== 'assigned') {
    throw new ApiError(400, "Assignment cannot be started in its current status");
  }

  // Update assignment status
  assignment.status = 'in_progress';
  assignment.startedAt = new Date();
  await assignment.save();

  res.status(200).json(
    new ApiResponse(200, assignment, "Assignment started successfully")
  );
});

/**
 * Submit assignment (model)
 * @route POST /api/v1/voice/assignments/:id/submit
 * @access Model only
 */
export const submitAssignment = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { modelNotes } = req.body;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid assignment ID");
  }

  if (req.user.role !== 'model') {
    throw new ApiError(403, "Only models can submit assignments");
  }

  const assignment = await VoiceAssignment.findById(id);
  
  if (!assignment || assignment.isDeleted) {
    throw new ApiError(404, "Assignment not found");
  }

  if (assignment.modelId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  if (!assignment.canSubmit()) {
    throw new ApiError(400, "Assignment cannot be submitted in its current status");
  }

  // Check if recording exists
  const recordings = await VoiceRecording.find({
    assignmentId: id,
    isDeleted: false,
    status: 'submitted'
  });

  if (recordings.length === 0) {
    throw new ApiError(400, "No valid recordings found. Please upload recordings first.");
  }

  // Update assignment using updateOne to avoid schema validation issues
  await VoiceAssignment.updateOne(
    { _id: id },
    {
      $set: {
        status: 'submitted',
        completedAt: new Date(),
        ...(modelNotes && { modelNotes })
      }
    }
  );

  // Get the updated assignment for response
  const updatedAssignment = await VoiceAssignment.findById(id);

  // Update all recordings status (they should already be submitted, but ensure consistency)
  await VoiceRecording.updateMany(
    {
      assignmentId: id,
      isDeleted: false
    },
    {
      status: 'submitted'
    }
  );

  res.status(200).json(
    new ApiResponse(200, updatedAssignment, "Assignment submitted successfully")
  );
});

/**
 * Save assignment progress (model)
 * @route POST /api/v1/voice/assignments/:id/save-progress
 * @access Model only
 */
export const saveAssignmentProgress = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { modelNotes } = req.body;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid assignment ID");
  }

  if (req.user.role !== 'model') {
    throw new ApiError(403, "Only models can save assignment progress");
  }

  const assignment = await VoiceAssignment.findById(id);
  
  if (!assignment || assignment.isDeleted) {
    throw new ApiError(404, "Assignment not found");
  }

  if (assignment.modelId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  if (!['assigned', 'in_progress', 'recorded'].includes(assignment.status)) {
    throw new ApiError(400, "Cannot save progress for assignment in current status");
  }

  // Update assignment to in_progress if it was just assigned
  if (assignment.status === 'assigned') {
    assignment.status = 'in_progress';
    assignment.startedAt = new Date();
  }

  // Save model notes if provided
  if (modelNotes !== undefined) {
    assignment.modelNotes = modelNotes;
  }
  
  // Use updateOne to avoid validation issues with assignedBy/agencyId
  await VoiceAssignment.updateOne(
    { _id: id },
    {
      $set: {
        status: assignment.status,
        startedAt: assignment.startedAt,
        modelNotes: assignment.modelNotes,
        updatedAt: new Date()
      }
    }
  );

  res.status(200).json(
    new ApiResponse(200, { id, status: assignment.status }, "Progress saved successfully")
  );
});

/**
 * Cancel assignment (agency)
 * @route POST /api/v1/voice/assignments/:id/cancel
 * @access Agency only
 */
export const cancelAssignment = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { reason } = req.body;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid assignment ID");
  }

  if (req.user.role !== 'agency') {
    throw new ApiError(403, "Only agencies can cancel assignments");
  }

  const assignment = await VoiceAssignment.findById(id);
  
  if (!assignment || assignment.isDeleted) {
    throw new ApiError(404, "Assignment not found");
  }

  if (assignment.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  if (['approved', 'cancelled'].includes(assignment.status)) {
    throw new ApiError(400, "Cannot cancel assignment in its current status");
  }

  // Update assignment
  assignment.status = 'cancelled';
  assignment.reviewComments = reason || 'Assignment cancelled by agency';
  assignment.reviewedBy = req.user._id;
  assignment.reviewedAt = new Date();
  
  await assignment.save();

  res.status(200).json(
    new ApiResponse(200, assignment, "Assignment cancelled successfully")
  );
});

/**
 * Bulk cancel assignments (agency)
 * @route POST /api/v1/voice/assignments/bulk-cancel
 * @access Agency only
 */
export const bulkCancelAssignments = asyncHandler(async (req, res) => {
  const { assignmentIds, reason } = req.body;
  
  if (!assignmentIds || !Array.isArray(assignmentIds) || assignmentIds.length === 0) {
    throw new ApiError(400, "Assignment IDs array is required");
  }

  if (req.user.role !== 'agency') {
    throw new ApiError(403, "Only agencies can cancel assignments");
  }

  // Validate all assignment IDs
  const invalidIds = assignmentIds.filter(id => !mongoose.Types.ObjectId.isValid(id));
  if (invalidIds.length > 0) {
    throw new ApiError(400, `Invalid assignment IDs: ${invalidIds.join(', ')}`);
  }

  // Find assignments
  const assignments = await VoiceAssignment.find({
    _id: { $in: assignmentIds },
    agencyId: req.user._id,
    isDeleted: false,
    status: { $nin: ['approved', 'cancelled'] }
  });

  if (assignments.length === 0) {
    throw new ApiError(400, "No valid assignments found to cancel");
  }

  // Update assignments
  const updateResult = await VoiceAssignment.updateMany(
    {
      _id: { $in: assignments.map(a => a._id) }
    },
    {
      status: 'cancelled',
      reviewComments: reason || 'Bulk cancelled by agency',
      reviewedBy: req.user._id,
      reviewedAt: new Date()
    }
  );

  res.status(200).json(
    new ApiResponse(200, {
      cancelledCount: updateResult.modifiedCount,
      requestedCount: assignmentIds.length,
      foundCount: assignments.length
    }, "Assignments cancelled successfully")
  );
});

/**
 * Complete assignment review (agency)
 * @route POST /api/v1/voice/assignments/:id/complete-review
 * @access Agency only
 */
export const completeAssignmentReview = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { comments } = req.body;

  if (req.user.role !== 'agency') {
    throw new ApiError(403, "Only agencies can complete assignment reviews");
  }

  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid assignment ID");
  }

  // Find assignment
  const assignment = await VoiceAssignment.findById(id);

  if (!assignment || assignment.isDeleted) {
    throw new ApiError(404, "Assignment not found");
  }

  if (assignment.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  // Check if all recordings are reviewed
  const recordings = await VoiceRecording.find({
    assignmentId: id,
    isDeleted: false
  });

  const pendingRecordings = recordings.filter(r =>
    !r.status || r.status === 'submitted' || r.status === 'pending'
  );

  if (pendingRecordings.length > 0) {
    throw new ApiError(400, `Cannot complete review. ${pendingRecordings.length} recordings are still pending review.`);
  }

  // Check if any recordings were rejected
  const rejectedRecordings = recordings.filter(r =>
    r.status === 'rejected' || r.status === 'requires_revision'
  );

  if (rejectedRecordings.length > 0) {
    // Assignment needs revision - set status to rejected for model to re-record
    assignment.status = 'rejected';
    assignment.reviewComments = comments || `${rejectedRecordings.length} recordings need revision. Please re-record the rejected items.`;
    assignment.reviewedBy = req.user._id;
    assignment.reviewedAt = new Date();

    await assignment.save();

    res.status(200).json(
      new ApiResponse(200, {
        assignment,
        rejectedRecordings: rejectedRecordings.length,
        status: 'revision_required'
      }, "Assignment review completed - revision required")
    );
  } else {
    // All recordings approved - complete the assignment
    assignment.status = 'approved';
    assignment.reviewComments = comments || 'All recordings approved';
    assignment.reviewedBy = req.user._id;
    assignment.reviewedAt = new Date();
    assignment.completedAt = new Date();

    await assignment.save();

    res.status(200).json(
      new ApiResponse(200, {
        assignment,
        approvedRecordings: recordings.length,
        status: 'completed'
      }, "Assignment review completed successfully - all recordings approved")
    );
  }
});

/**
 * Get assignment statistics
 * @route GET /api/v1/voice/assignments/stats
 * @access Model and Agency
 */
export const getAssignmentStats = asyncHandler(async (req, res) => {
  let matchStage = { isDeleted: false };
  
  if (req.user.role === 'model') {
    matchStage.modelId = req.user._id;
  } else if (req.user.role === 'agency') {
    matchStage.agencyId = req.user._id;
  } else {
    throw new ApiError(403, "Access denied");
  }

  const { startDate, endDate } = req.query;
  
  if (startDate && endDate) {
    matchStage.assignedAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }

  // Get status breakdown
  const statusStats = await VoiceAssignment.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        avgCompletionTime: {
          $avg: {
            $cond: {
              if: { $and: ['$startedAt', '$completedAt'] },
              then: { $subtract: ['$completedAt', '$startedAt'] },
              else: null
            }
          }
        }
      }
    }
  ]);

  // Get priority breakdown
  const priorityStats = await VoiceAssignment.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$priority',
        count: { $sum: 1 }
      }
    }
  ]);

  // Get overdue assignments count
  const overdueCount = await VoiceAssignment.countDocuments({
    ...matchStage,
    deadline: { $lt: new Date() },
    status: { $nin: ['approved', 'cancelled'] }
  });

  // Get average rating (for agency)
  let avgRating = null;
  if (req.user.role === 'agency') {
    const ratingResult = await VoiceAssignment.aggregate([
      { 
        $match: { 
          ...matchStage, 
          rating: { $exists: true, $ne: null } 
        } 
      },
      {
        $group: {
          _id: null,
          avgRating: { $avg: '$rating' },
          totalRated: { $sum: 1 }
        }
      }
    ]);
    
    avgRating = ratingResult[0] || { avgRating: 0, totalRated: 0 };
  }

  res.status(200).json(
    new ApiResponse(200, {
      statusStats,
      priorityStats,
      overdueCount,
      avgRating
    }, "Assignment statistics retrieved successfully")
  );
});

/**
 * Update assignment deadline (agency)
 * @route PUT /api/v1/voice/assignments/:id/deadline
 * @access Agency only
 */
export const updateAssignmentDeadline = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { deadline, reason } = req.body;
  
  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new ApiError(400, "Invalid assignment ID");
  }

  if (req.user.role !== 'agency') {
    throw new ApiError(403, "Only agencies can update assignment deadlines");
  }

  if (!deadline) {
    throw new ApiError(400, "New deadline is required");
  }

  const newDeadline = new Date(deadline);
  if (newDeadline <= new Date()) {
    throw new ApiError(400, "Deadline must be in the future");
  }

  const assignment = await VoiceAssignment.findById(id);
  
  if (!assignment || assignment.isDeleted) {
    throw new ApiError(404, "Assignment not found");
  }

  if (assignment.agencyId.toString() !== req.user._id.toString()) {
    throw new ApiError(403, "Access denied");
  }

  if (['approved', 'cancelled'].includes(assignment.status)) {
    throw new ApiError(400, "Cannot update deadline for completed assignments");
  }

  // Update deadline
  const oldDeadline = assignment.deadline;
  assignment.deadline = newDeadline;
  
  // Add to revision history if reason provided
  if (reason) {
    assignment.revisionHistory.push({
      type: 'deadline_update',
      reason,
      changedBy: req.user._id,
      changedAt: new Date(),
      oldValue: oldDeadline,
      newValue: newDeadline
    });
  }
  
  await assignment.save();

  res.status(200).json(
    new ApiResponse(200, assignment, "Assignment deadline updated successfully")
  );
});