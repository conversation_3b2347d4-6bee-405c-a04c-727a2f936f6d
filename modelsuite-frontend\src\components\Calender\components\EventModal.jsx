import { useState, useEffect } from "react";
import {
  X,
  Users,
  LinkIcon,
  FileText,
  Calendar,
  Clock,
  Globe,
} from "lucide-react";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

dayjs.extend(utc);
dayjs.extend(timezone);

const TIMEZONES = [
  {
    value: "Asia/Kolkata",
    label: "India Standard Time (IST) — UTC +5:30 — Delhi, Mumbai, Bangalore",
  },
  {
    value: "America/New_York",
    label: "Eastern Time (ET) — UTC -5 / -4 — New York, Toronto, Atlanta",
  },
  {
    value: "America/Chicago",
    label: "Central Time (CT) — UTC -6 / -5 — Chicago, Dallas, Mexico City",
  },
  {
    value: "America/Denver",
    label: "Mountain Time (MT) — UTC -7 / -6 — Denver, Calgary, Phoenix",
  },
  {
    value: "America/Los_Angeles",
    label: "Pacific Time (PT) — UTC -8 / -7 — Los Angeles, Seattle, Vancouver",
  },
  {
    value: "Europe/London",
    label: "Greenwich Mean Time (GMT) — UTC ±0 — London (winter), Reykjavik",
  },
  {
    value: "Europe/Paris",
    label:
      "Central European Time (CET/CEST) — UTC +1 / +2 — Berlin, Paris, Rome, Madrid",
  },
  {
    value: "Asia/Tokyo",
    label: "Japan Standard Time (JST) — UTC +9 — Tokyo, Osaka, Hiroshima",
  },
  {
    value: "Asia/Shanghai",
    label: "China Standard Time (CST) — UTC +8 — Beijing, Shanghai, Hong Kong",
  },
  {
    value: "Australia/Sydney",
    label:
      "Australian Eastern Time (AET) — UTC +10 / +11 — Sydney, Melbourne, Brisbane",
  },
  {
    value: "UTC",
    label:
      "Coordinated Universal Time (UTC) — UTC ±0 — Used globally (e.g. servers)",
  },
];

const EventModal = ({
  isOpen,
  onClose,
  onSave,
  selectedDate,
  selectedEvent,
  canEdit = true,
}) => {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    date: "",
    startTime: "",
    endTime: "",
    timezone: "",
    guests: [],
  });
  const [guestInput, setGuestInput] = useState("");

  useEffect(() => {
    const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    if (selectedEvent) {
      const event = selectedEvent;
      const startDate = dayjs(event.start).tz(userTimeZone);
      const endDate = dayjs(event.end).tz(userTimeZone);

      setFormData({
        title: event.title || "",
        description: event.description || "",
        date: startDate.format("YYYY-MM-DD"),
        startTime: startDate.format("HH:mm"),
        endTime: endDate.format("HH:mm"),
        timezone: event.timezone || userTimeZone,
        guests:
          event.extendedProps?.guests?.map((g) =>
            typeof g === "string" ? g : g.email,
          ) || [],
      });
    } else if (selectedDate) {
      const startDate = dayjs(selectedDate).tz(userTimeZone);
      const endDate = startDate.add(1, "hour");

      setFormData({
        title: "",
        description: "",
        date: startDate.format("YYYY-MM-DD"),
        startTime: startDate.format("HH:mm"),
        endTime: endDate.format("HH:mm"),
        timezone: userTimeZone,
        guests: [],
      });
    }
  }, [selectedEvent, selectedDate]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleAddGuest = (e) => {
    e.preventDefault();
    if (guestInput.trim() && !formData.guests.includes(guestInput.trim())) {
      setFormData((prev) => ({
        ...prev,
        guests: [...prev.guests, guestInput.trim()],
      }));
      setGuestInput("");
    }
  };

  const handleRemoveGuest = (guestToRemove) => {
    setFormData((prev) => ({
      ...prev,
      guests: prev.guests.filter((guest) => guest !== guestToRemove),
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!formData.title.trim()) {
      alert("Please enter an event title");
      return;
    }
    if (!formData.date || !formData.startTime || !formData.endTime) {
      alert("Please select date and time");
      return;
    }

    const startDateTime = dayjs.tz(
      `${formData.date} ${formData.startTime}`,
      formData.timezone,
    );
    const endDateTime = dayjs.tz(
      `${formData.date} ${formData.endTime}`,
      formData.timezone,
    );

    if (endDateTime.isBefore(startDateTime)) {
      alert("End time must be after start time");
      return;
    }

    onSave({
      title: formData.title,
      description: formData.description,
      start: startDateTime.toISOString(),
      end: endDateTime.toISOString(),
      guests: formData.guests,
      timezone: formData.timezone,
    });
  };

  if (!isOpen) return null;

  const isViewOnly = selectedEvent && !canEdit;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b dark:border-zinc-700">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
            {selectedEvent
              ? isViewOnly
                ? "Event Details"
                : "Edit Event"
              : "Create New Event"}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-zinc-800 rounded-lg transition"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Event Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Event Name
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              placeholder="New Event Title"
              disabled={isViewOnly}
              className="w-full px-3 py-2 border border-gray-300 dark:border-zinc-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-zinc-800 dark:text-gray-200 disabled:opacity-50"
              required
            />
          </div>

          {/* Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Calendar className="w-4 h-4 inline mr-1" />
              Date
            </label>
            <input
              type="date"
              name="date"
              value={formData.date}
              onChange={handleInputChange}
              disabled={isViewOnly}
              className="w-full px-3 py-2 border border-gray-300 dark:border-zinc-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-zinc-800 dark:text-gray-200 disabled:opacity-50"
              required
            />
          </div>

          {/* Time */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Clock className="w-4 h-4 inline mr-1" />
              Time
            </label>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                  Start Time
                </label>
                <input
                  type="time"
                  name="startTime"
                  value={formData.startTime}
                  onChange={handleInputChange}
                  disabled={isViewOnly}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-zinc-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-zinc-800 dark:text-gray-200 text-sm disabled:opacity-50"
                  required
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                  End Time
                </label>
                <input
                  type="time"
                  name="endTime"
                  value={formData.endTime}
                  onChange={handleInputChange}
                  disabled={isViewOnly}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-zinc-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-zinc-800 dark:text-gray-200 text-sm disabled:opacity-50"
                  required
                />
              </div>
            </div>
          </div>

          {/* Timezone */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Globe className="w-4 h-4 inline mr-1" />
              Timezone
            </label>
            <select
              name="timezone"
              value={formData.timezone}
              onChange={handleInputChange}
              disabled={isViewOnly}
              className="w-full px-3 py-2 border border-gray-300 dark:border-zinc-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-zinc-800 dark:text-gray-200 disabled:opacity-50"
            >
              {TIMEZONES.map((tz) => (
                <option key={tz.value} value={tz.value}>
                  {tz.label}
                </option>
              ))}
            </select>
          </div>

          {/* Google Meet Info */}
          {(selectedEvent?.extendedProps?.meetLink || !isViewOnly) && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
              <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                <LinkIcon className="w-4 h-4" />
                <span className="text-sm font-medium">
                  {selectedEvent?.extendedProps?.meetLink
                    ? "Google Meet Link Available"
                    : selectedEvent?.extendedProps?.guests?.length > 0
                      ? "Google Meet link will be created automatically"
                      : "Google Meet link not required (no guests)"}
                </span>
              </div>
              {selectedEvent?.extendedProps?.meetLink && (
                <a
                  href={selectedEvent.extendedProps.meetLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-blue-600 dark:text-blue-400 hover:underline mt-1 block"
                >
                  {selectedEvent.extendedProps.meetLink}
                </a>
              )}
            </div>
          )}

          {/* Add Guests */}
          {!isViewOnly && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <Users className="w-4 h-4 inline mr-1" />
                Add Guests
              </label>
              <div className="flex gap-2 mb-3">
                <input
                  type="email"
                  value={guestInput}
                  onChange={(e) => setGuestInput(e.target.value)}
                  placeholder="Enter email address"
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-zinc-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-zinc-800 dark:text-gray-200 text-sm"
                />
                <button
                  type="button"
                  onClick={handleAddGuest}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition text-sm"
                >
                  Add
                </button>
              </div>
              {formData.guests.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.guests.map((guest, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 rounded-full text-sm"
                    >
                      {guest}
                      <button
                        type="button"
                        onClick={() => handleRemoveGuest(guest)}
                        className="ml-1 hover:bg-blue-200 dark:hover:bg-blue-800 rounded-full p-0.5"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* View Only Guests */}
          {isViewOnly && formData.guests.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <Users className="w-4 h-4 inline mr-1" />
                Guests
              </label>
              <div className="flex flex-wrap gap-2">
                {formData.guests.map((guest, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 bg-gray-100 dark:bg-zinc-700 text-gray-700 dark:text-gray-300 rounded-full text-sm"
                  >
                    {guest}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <FileText className="w-4 h-4 inline mr-1" />
              Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Add description..."
              disabled={isViewOnly}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-zinc-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-zinc-800 dark:text-gray-200 resize-none disabled:opacity-50"
            />
          </div>

          {/* Buttons */}
          {!isViewOnly && (
            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-zinc-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-zinc-800 transition"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition"
              >
                {selectedEvent ? "Update" : "Save"}
              </button>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default EventModal;
