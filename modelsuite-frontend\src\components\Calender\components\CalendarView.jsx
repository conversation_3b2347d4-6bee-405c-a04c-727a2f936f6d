"use client";

import { useState, useEffect, useRef } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import interactionPlugin from "@fullcalendar/interaction";
import axios from "axios";
import { toast } from "react-hot-toast";
import { CalendarPlus, Clock, MapPin, Plus } from "lucide-react";
import EventModal from "./EventModal";

const CalendarView = ({ modelId, isModel }) => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showEventModal, setShowEventModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [currentView, setCurrentView] = useState("dayGridMonth");
  const baseUrl = import.meta.env.VITE_API_BASE_URL;

  const calendarRef = useRef(null);

  useEffect(() => {
    fetchEvents();
  }, [modelId]);

  const fetchEvents = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${baseUrl}/event/get/${modelId}`);
      const formattedEvents = response.data.map((event) => ({
        id: event.id,
        title: event.title,
        start: event.start,
        end: event.end,
        description: event.description,
        timezone: event.timezone,
        extendedProps: {
          meetLink: event.meetLink,
        },
      }));
      setEvents(formattedEvents);
    } catch (error) {
      console.error("Error fetching events:", error);
      toast.error("Failed to fetch calendar events");
    } finally {
      setLoading(false);
    }
  };

  const handleDateClick = (arg) => {
    if (!isModel) {
      setSelectedDate(arg.date);
      setSelectedEvent(null);
      setShowEventModal(true);
    }
  };

  const handleEventClick = (clickInfo) => {
    setSelectedEvent(clickInfo.event);
    setShowEventModal(true);
  };

  const handleEventCreate = async (eventData) => {
    try {
      const response = await axios.post(`${baseUrl}/event/create`, {
        modelId,
        ...eventData,
      });

      toast.success("Event created successfully!");
      setShowEventModal(false);
      fetchEvents(); // Refresh events

      if (response.data.meetLink) {
        toast.info("Google Meet link created!");
      }
    } catch (error) {
      console.error("Error creating event:", error);
      toast.error(error.response?.data?.message || "Failed to create event");
    }
  };

  const handleViewChange = (newView) => {
    setCurrentView(newView);
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      calendarApi.changeView(newView);
    }
  };

  // Custom event content renderer
  const renderEventContent = (eventInfo) => {
    const title = eventInfo.event.title;
    const maxLength = currentView === "dayGridMonth" ? 15 : 25; // Shorter for month view
    const truncatedTitle =
      title.length > maxLength ? title.substring(0, maxLength) + "..." : title;

    return (
      <div className="fc-event-custom" title={title}>
        <span className="fc-event-title-custom">{truncatedTitle}</span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600 dark:text-gray-400">
          Loading calendar...
        </span>
      </div>
    );
  }

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-[#181c2a] via-[#23293a] to-[#1e2233] py-12 px-2">
      {/* Card Container */}
      <div className="max-w-7xl mx-auto rounded-3xl shadow-2xl border border-[#35374a] bg-[rgba(36,41,58,0.92)] backdrop-blur-xl p-10 flex flex-col gap-10">
        {/* Top Right Button */}
        <div className="flex justify-end items-center mb-2">
          {!isModel && (
            <button
              onClick={() => {
                setSelectedDate(new Date());
                setSelectedEvent(null);
                setShowEventModal(true);
              }}
              className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-[#a78bfa] to-[#8b5cf6] hover:from-[#b993ff] hover:to-[#8ca6db] text-white rounded-full shadow-lg font-semibold text-lg transition-all duration-200 ring-2 ring-[#a78bfa] ring-offset-2 ring-offset-[#23293a]"
              style={{ boxShadow: "0 4px 24px 0 rgba(167,139,250,0.18)" }}
            >
              <Plus className="w-6 h-6" />
              New Event
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
          {/* Events List */}
          <div className="lg:col-span-1 animate-fadeInUp">
            <div className="bg-[rgba(36,41,58,0.98)] rounded-2xl border border-[#35374a] shadow-lg p-7 flex flex-col justify-center items-center">
              <h3 className="text-2xl font-bold text-[#a78bfa] mb-6 tracking-tight">
                Upcoming Events
              </h3>
              <div className="space-y-5 max-h-[28rem] w-full overflow-y-auto custom-scrollbar pr-2">
                {events.length === 0 ? (
                  <div className="text-center py-16 text-[#a78bfa] flex flex-col items-center justify-center">
                    <CalendarPlus className="w-16 h-16 mb-5 opacity-60 text-[#a78bfa]" />
                    <p className="text-xl font-semibold">No events scheduled</p>
                  </div>
                ) : (
                  events.map((event) => (
                    <EventCard
                      key={event.id}
                      event={event}
                      onClick={() => handleEventClick({ event })}
                    />
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Calendar */}
          <div className="lg:col-span-2 animate-fadeInUp">
            <div className="bg-[rgba(36,41,58,0.98)] rounded-3xl p-8 border border-[#35374a] shadow-lg">
              {/* View Toggle Buttons */}
              <div className="flex justify-center mb-8">
                <div className="inline-flex rounded-full border border-[#35374a] bg-[#23293a] p-2 shadow-lg gap-2">
                  <button
                    onClick={() => handleViewChange("dayGridMonth")}
                    className={`px-8 py-2 text-lg font-bold rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-[#a78bfa] focus:ring-offset-2 focus:ring-offset-[#23293a] ${
                      currentView === "dayGridMonth"
                        ? "bg-gradient-to-r from-[#a78bfa] to-[#8b5cf6] text-white shadow-lg ring-4 ring-[#a78bfa]"
                        : "text-[#a78bfa] hover:bg-[#23293a]"
                    }`}
                  >
                    Month
                  </button>
                  <button
                    onClick={() => handleViewChange("dayGridWeek")}
                    className={`px-8 py-2 text-lg font-bold rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-[#a78bfa] focus:ring-offset-2 focus:ring-offset-[#23293a] ${
                      currentView === "dayGridWeek"
                        ? "bg-gradient-to-r from-[#a78bfa] to-[#8b5cf6] text-white shadow-lg ring-4 ring-[#a78bfa]"
                        : "text-[#a78bfa] hover:bg-[#23293a]"
                    }`}
                  >
                    Week
                  </button>
                </div>
              </div>
              <FullCalendar
                ref={calendarRef}
                plugins={[dayGridPlugin, interactionPlugin]}
                initialView={currentView}
                events={events}
                dateClick={handleDateClick}
                eventClick={handleEventClick}
                eventContent={renderEventContent}
                height="auto"
                headerToolbar={{
                  left: "prev,next today",
                  center: "title",
                  right: "",
                }}
                eventDisplay="block"
                dayMaxEvents={3}
                moreLinkClick="popover"
                eventClassNames="cursor-pointer"
              />
              {/* Custom FullCalendar Styles */}
              <style jsx>{`
                .fc-col-header-cell {
                  background-color: rgba(36, 41, 58, 0.92) !important;
                  border-color: #35374a !important;
                  backdrop-filter: blur(8px);
                }
                .fc-col-header-cell-cushion {
                  color: #a78bfa !important;
                  font-weight: 700 !important;
                  padding: 12px 8px !important;
                  font-size: 1.1rem !important;
                }
                .fc-daygrid-day-number {
                  color: #a78bfa !important;
                  font-weight: 600 !important;
                  font-size: 1rem !important;
                }
                .fc-day-today {
                  background: linear-gradient(
                    90deg,
                    #a78bfa 0%,
                    #8b5cf6 100%
                  ) !important;
                  border-radius: 12px !important;
                }
                .fc-day-today .fc-daygrid-day-number {
                  color: #fff !important;
                  font-weight: 700 !important;
                }
                .fc-daygrid-day {
                  border-color: #35374a !important;
                }
                /* Custom Event Styling */
                .fc-event-custom {
                  padding: 4px 10px !important;
                  border-radius: 8px !important;
                  font-size: 14px !important;
                  line-height: 1.4 !important;
                  overflow: hidden !important;
                  white-space: nowrap !important;
                  text-overflow: ellipsis !important;
                  background: linear-gradient(
                    90deg,
                    #a78bfa 0%,
                    #8b5cf6 100%
                  ) !important;
                  border: none !important;
                  color: #fff !important;
                  cursor: pointer !important;
                  margin-bottom: 4px !important;
                  box-shadow: 0 2px 8px 0 rgba(167, 139, 250, 0.15);
                }
                .fc-event-title-custom {
                  font-weight: 700 !important;
                  font-size: 14px !important;
                  overflow: hidden !important;
                  text-overflow: ellipsis !important;
                  white-space: nowrap !important;
                  display: block !important;
                }
                .fc-daygrid-event {
                  margin-top: 4px !important;
                  margin-bottom: 4px !important;
                }
                .fc-event {
                  border: none !important;
                  background: linear-gradient(
                    90deg,
                    #a78bfa 0%,
                    #8b5cf6 100%
                  ) !important;
                  color: #fff !important;
                }
                .fc-event:hover {
                  background: #a78bfa !important;
                }
              `}</style>
            </div>
          </div>
        </div>

        {/* Event Modal */}
        {showEventModal && (
          <EventModal
            isOpen={showEventModal}
            onClose={() => setShowEventModal(false)}
            onSave={handleEventCreate}
            selectedDate={selectedDate}
            selectedEvent={selectedEvent}
            canEdit={!isModel}
          />
        )}
      </div>
    </div>
  );
};

const EventCard = ({ event, onClick }) => {
  const startDate = new Date(event.start);
  const endDate = new Date(event.end);

  return (
    <div
      onClick={onClick}
      className="bg-[#23293a] border border-[#35374a] rounded-xl p-5 cursor-pointer hover:shadow-lg transition-shadow group animate-fadeInUp"
    >
      <h4 className="font-semibold text-[#a78bfa] mb-2 text-lg group-hover:text-[#a78bfa] transition-colors truncate">
        {event.title}
      </h4>
      <div className="space-y-1 text-sm text-zinc-400">
        <div className="flex items-center gap-2">
          <Clock className="w-4 h-4 text-[#a78bfa]" />
          <span>
            {startDate.toLocaleDateString()} •{" "}
            {startDate.toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
            {endDate &&
              ` - ${endDate.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}`}
          </span>
        </div>
        {event.extendedProps?.meetLink && (
          <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4 text-[#a78bfa]" />
            <span className="text-[#a78bfa]">Google Meet</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default CalendarView;
