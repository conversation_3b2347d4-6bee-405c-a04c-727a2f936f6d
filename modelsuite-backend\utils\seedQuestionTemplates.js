import dotenv from "dotenv";
import QuestionSection from "../models/voice/QuestionSection.js";
import QuestionTemplate from "../models/voice/QuestionTemplate.js";
import connectDB from "../db.js";

// Load environment variables
dotenv.config();

// Default question sections and templates
const defaultSections = [
  {
    title: "Good Morning (Flirty/Sensual)",
    description: "Warm, sleepy, soft girlfriend energy — as if just waking up",
    isDefault: true,
    tags: ["good_morning", "flirty", "sensual", "intimate", "naughty"],
    questions: [
      "Good morning, baby. Did you sleep well… or were you thinking about me all night?",
      "Mmm… I just woke up and I'm already wet thinking about you.",
      "I wish you were here to wake me up with your hands all over my body.",
      "Stretching in bed… wearing just a little shirt and nothing else. Wanna see?",
      "I dreamed of you last night… and let's just say I woke up needing more.",
      "Still in bed… do you want to join me under the covers?",
      "Morning kisses? Or do you prefer something a little dirtier to start your day?",
      "I can't stop thinking about your hands… and what they'd do to me right now.",
      "Rise and shine, baby. Or should I say… rise and grind?",
      "The first thing I touched this morning wasn't my phone… guess what it was?",
      "Good morning. I'm dripping already… and it's all your fault.",
      "Imagine waking up to me on top of you, kissing down your chest slowly…",
      "I haven't even had coffee yet… but I'm craving something else.",
      "What if I called you every morning with a naughty surprise like this?",
      "I just want to crawl into your bed and ride you awake…",
      "Still wearing nothing from last night. Should I send you a pic?",
      "My morning routine? Thinking of you, teasing myself… then maybe finishing.",
      "If you were here, I wouldn't let you leave the bed… not without making me scream first.",
      "Let's start the day with a moan, not a yawn.",
      "Shhh… I'm touching myself under the sheets. Just for you. Don't tell anyone."
    ]
  },
  {
    title: "Good Morning (Affectionate, Non-Sexual)",
    description: "Warm, sleepy, soft girlfriend energy — as if just waking up",
    isDefault: true,
    tags: ["good_morning", "affectionate", "non_sexual", "loving", "sleepy", "soft_spoken"],
    questions: [
      "Good morning, baby… I just woke up, my voice is all sleepy, but I wanted you to be the first person I say hi to.",
      "Hi sweetie… I'm still lying in bed, barely awake. Just wanted to start your day with some love.",
      "Morning, honey… the sunlight's peeking in, and I'm wrapped up in my blanket thinking of you.",
      "Hey love… I literally just opened my eyes. I haven't even gotten up yet. Good morning from my cozy little cocoon.",
      "Baby, it's morning. Can we just stay like this a few more minutes? I'm not ready to leave the blanket yet.",
      "Mmm… good morning, sweetie. I'm still waking up, but I wanted to send you a soft little message first.",
      "Hi love… I had a weird dream and I just woke up from it. You're the first person I thought of.",
      "Hey sweetheart… it's one of those mornings where I want to stay in bed forever. You too?",
      "Good morning, my darling. I hope you slept okay… I just opened my eyes and wanted to check in.",
      "Honey, I'm still under the covers. It's quiet, warm, and I'm feeling peaceful. Hope you are too.",
      "Morning, baby. I'm still sleepy, my eyes are barely open… but I wanted to whisper something nice to start your day.",
      "Sweetie, I haven't even checked my phone yet. Just wanted to send love first thing.",
      "Hi honey… it's morning again. New start, fresh breath, slow stretch. Let's take it easy today.",
      "I just yawned so hard, baby… I'm still in bed, not quite ready to move yet. But I wanted to say good morning.",
      "Morning, love… today's a clean slate. Let's go gently, okay?",
      "Baby, I just made my bed hair even worse by rolling around… good morning from my messy self.",
      "Hey sweetie… I've got my warm socks on and my sleepy face. Hope you're waking up okay too.",
      "Good morning, my love. The world feels quiet and soft right now. Let's hold on to that.",
      "Still half-asleep, honey… just wanted to send this before I get up and start my day.",
      "Hi baby. Just so you know — you were my first thought this morning. Always are."
    ]
  },
  {
    title: "Good Night (Affectionate, Non-Sexual)",
    description: "Girlfriend-style, soft-spoken, loving, sleepy",
    isDefault: true,
    tags: ["good_night", "affectionate", "non_sexual", "loving", "sleepy", "caring"],
    questions: [
      "Good night, baby... I'm all snuggled in bed, just wanted to whisper something sweet before I fall asleep.",
      "Sweetie, it's bedtime. I hope you're already tucked in and letting the day fade away.",
      "Hey love... I'm lying here with my blanket up to my chin, thinking of you before I drift off.",
      "Night night, honey. Close your eyes and rest — you did your best today.",
      "I'm so sleepy, baby. Just wanted to tell you I'm proud of you before I go to sleep.",
      "The lights are off, the room's quiet, and I'm thinking of you, sweetie. Sleep well, okay?",
      "I'm already curled up, love… just breathing slow, hoping you're feeling peaceful too.",
      "Good night, my darling. I know some days are heavy, but rest will help. I'm right here.",
      "Baby, wrap yourself up and get cozy. You deserve the softest, sweetest sleep tonight.",
      "Sweetheart, we made it through another day. Let's both rest now — tomorrow can wait.",
      "I'm whispering just for you, love. I want your last thought tonight to be warm and gentle.",
      "Honey, I'm lying on my side, eyes getting heavy... I hope you feel close to me somehow.",
      "Baby, it's okay to be tired. You don't have to be strong right now. Just rest.",
      "Close your eyes, sweetie. I'm sending all my softest thoughts to you tonight.",
      "Darling, breathe with me for a second. In… and out. Let the day melt away.",
      "I'm in bed, love. Still and quiet. Wanted to tell you one last time — you're doing okay.",
      "Baby, you made it to the end of the day. That matters. Let's rest our hearts together now.",
      "Sweetie, turn the world off for now. You can come back to everything tomorrow.",
      "I'm so comfy right now, honey... I hope you are too. You deserve softness and rest.",
      "Good night, love. I'll be here again tomorrow, same voice, same care, same cozy space."
    ]
  },
  {
    title: "Daytime Conversation Starter (Mixed Tone)",
    description: "Girlfriend-style, soft-spoken, loving, excited",
    isDefault: true,
    tags: ["conversation_starter", "mixed_tone", "caring", "casual", "playful"],
    questions: [
      "Hey, what are you up to right now?",
      "Sweetie, I was just thinking about you and figured I'd check in.",
      "Have you had a moment to breathe today? Like really stop and just… breathe?",
      "Baby, I want to hear something good from your day. Just one nice little thing.",
      "Is today going by fast for you, or dragging like crazy?",
      "Love, what's something small you've done today that you're proud of?",
      "Hey, I just made a cup of tea. What's your go-to drink when you need to reset?",
      "Okay, random question: What's your favorite kind of weather mood?",
      "Honey, how's your head and heart doing today? Just wondering.",
      "What's something you wish more people asked you about?",
      "Just checking in. You don't have to say much — just let me know you're okay.",
      "Darling, I hope today's being kind to you. If not, I am.",
      "What's something small that made you smile today?",
      "Hey, if we could press pause on the day right now… what would you do with that quiet moment?",
      "Baby, are you taking care of yourself a little today? Even just the basics?",
      "Tell me something totally random you saw or noticed today. I love hearing your little observations.",
      "What kind of day is it for you — productive, lazy, emotional, chaotic?",
      "Sweetheart, I hope you've had at least one deep breath and a snack today.",
      "I don't have anything clever to say — I just wanted to be in your day for a second.",
      "You don't need to have anything figured out right now. Just being here is enough."
    ]
  }
];

export const seedQuestionTemplates = async () => {
  try {
    console.log("🌱 Starting question templates seeding...");

    // Check if questions already exist
    const existingSections = await QuestionSection.find({ isDefault: true });
    if (existingSections.length > 0) {
      console.log("✅ Default question sections already exist. Skipping seeding.");
      return;
    }

    // Create sections and questions
    for (const sectionData of defaultSections) {
      console.log(`📝 Creating section: ${sectionData.title}`);
      
      // Create section
      const section = await QuestionSection.create({
        title: sectionData.title,
        description: sectionData.description,
        isDefault: sectionData.isDefault,
        createdBy: null // Default sections have no creator
      });

      // Create questions for this section
      const questionPromises = sectionData.questions.map((questionText, index) => {
        return QuestionTemplate.create({
          text: questionText,
          sectionId: section._id,
          tags: sectionData.tags || [], // Use section tags for all questions in that section
          createdBy: null // Default questions have no creator
        });
      });

      await Promise.all(questionPromises);
      console.log(`✅ Created ${sectionData.questions.length} questions for section: ${sectionData.title}`);
    }

    console.log("🎉 Question templates seeding completed successfully!");
    
    // Log summary
    const totalSections = await QuestionSection.countDocuments({ isDefault: true });
    const totalQuestions = await QuestionTemplate.countDocuments({ createdBy: null });
    console.log(`📊 Summary: ${totalSections} sections, ${totalQuestions} questions created`);

  } catch (error) {
    console.error("❌ Error seeding question templates:", error);
    throw error;
  }
};

export default seedQuestionTemplates;

// Run seeding if this file is executed directly
console.log('🚀 Starting question templates seeding...');
(async () => {
  try {
    console.log('📡 Connecting to database...');
    await connectDB();
    console.log('🌱 Running seeding function...');
    await seedQuestionTemplates();
    console.log('✅ Seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Failed to seed question templates:', error);
    process.exit(1);
  }
})();
