import mongoose from "mongoose";

/**
 * Voice Recording Model - Represents individual audio recordings uploaded by models
 * Stores file metadata, processing status, and quality metrics
 */
const voiceRecordingSchema = new mongoose.Schema(
  {
    // Core relationships
    assignmentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "VoiceAssignment",
      required: [true, "Assignment ID is required"]
    },
    questionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "QuestionTemplate",
      required: [true, "Question ID is required"]
    },
    questionText: {
      type: String,
      required: [true, "Question text is required"],
      trim: true
    },
    questionTags: {
      type: [String],
      default: [],
      index: true
    },
    modelId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ModelUser",
      required: [true, "Model ID is required"]
    },
    agencyId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Agency",
      required: [true, "Agency ID is required"]
    },
    
    // Remove sentence-based recording support and recording type - now all are question-based
    // recordingMode, sentenceId, sentenceOrder, recordingType, sentenceText - removed

    // File information
    originalFilename: {
      type: String,
      required: [true, "Original filename is required"],
      trim: true
    },
    generatedFilename: {
      type: String,
      required: [true, "Generated filename is required"],
      trim: true,
      unique: true
    },
    fileUrl: {
      type: String,
      required: [true, "File URL is required"],
      trim: true
    },
    cloudinaryPublicId: {
      type: String,
      required: [true, "Cloudinary public ID is required"],
      trim: true
    },
    
    // File metadata
    fileSize: {
      type: Number, // in bytes
      required: [true, "File size is required"],
      min: [1, "File size must be greater than 0"]
    },
    duration: {
      type: Number, // in seconds
      required: [true, "Duration is required"],
      min: [0.1, "Duration must be at least 0.1 seconds"]
    },
    format: {
      type: String,
      required: [true, "File format is required"],
      enum: ["mp3", "wav", "m4a", "ogg", "webm"],
      lowercase: true
    },
    mimeType: {
      type: String,
      required: [true, "MIME type is required"]
    },
    
    // Audio technical specifications
    bitrate: {
      type: String, // e.g., "128kbps", "320kbps"
      trim: true
    },
    sampleRate: {
      type: Number, // e.g., 44100, 48000
      min: [8000, "Sample rate too low"],
      max: [192000, "Sample rate too high"]
    },
    channels: {
      type: Number, // 1 for mono, 2 for stereo
      enum: [1, 2],
      default: 1
    },
    codec: {
      type: String,
      trim: true
    },
    
    // Recording session information
    recordingStartTime: {
      type: Date,
      default: null
    },
    recordingEndTime: {
      type: Date,
      default: null
    },
    recordingDevice: {
      type: String,
      trim: true,
      default: null
    },
    browserInfo: {
      userAgent: String,
      browser: String,
      version: String,
      platform: String
    },
    
    // Processing and quality
    processingStatus: {
      type: String,
      enum: ["pending", "processing", "completed", "failed"],
      default: "pending"
    },
    qualityScore: {
      type: Number,
      min: 0,
      max: 100,
      default: null
    },
    qualityMetrics: {
      noiseLevel: Number,      // 0-100, lower is better
      volumeLevel: Number,     // 0-100, optimal range 40-80
      clarityScore: Number,    // 0-100, higher is better
      backgroundNoise: Boolean,
      clipping: Boolean,       // Audio clipping detected
      silenceRatio: Number     // Percentage of silence
    },
    
    // Content analysis (optional AI features)
    transcription: {
      text: String,
      confidence: Number, // 0-1
      language: String,
      processingTime: Number // milliseconds
    },
    contentAnalysis: {
      keywordMatch: Number,    // 0-100, how well it matches script
      sentimentScore: Number,  // -1 to 1
      emotionTags: [String],   // detected emotions
      inappropriateContent: Boolean,
      contentWarnings: [String]
    },
    
    // Upload and submission tracking
    uploadStartedAt: {
      type: Date,
      default: Date.now
    },
    uploadCompletedAt: {
      type: Date,
      default: null
    },
    submittedAt: {
      type: Date,
      default: null
    },
    
    // Status and lifecycle
    status: {
      type: String,
      enum: [
        "uploading",    // File is being uploaded
        "processing",   // File is being processed
        "draft",        // Saved as draft, not submitted
        "submitted",    // Submitted for review
        "approved",     // Approved by agency
        "rejected",     // Rejected, needs rework
        "archived"      // Archived/replaced
      ],
      default: "uploading"
    },
    
    // Version control
    version: {
      type: Number,
      default: 1
    },
    isLatestVersion: {
      type: Boolean,
      default: true
    },
    replacedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "VoiceRecording",
      default: null
    },
    
    // Model notes and metadata
    modelNotes: {
      type: String,
      trim: true,
      maxlength: [500, "Model notes cannot exceed 500 characters"]
    },
    recordingAttempts: {
      type: Number,
      default: 1,
      min: 1
    },
    
    // Error handling
    uploadErrors: [{
      timestamp: Date,
      error: String,
      errorCode: String,
      resolved: { type: Boolean, default: false }
    }],
    processingErrors: [{
      timestamp: Date,
      error: String,
      stage: String, // upload, processing, analysis
      resolved: { type: Boolean, default: false }
    }],
    
    // Security and access
    accessLevel: {
      type: String,
      enum: ["private", "agency_only", "public"],
      default: "agency_only"
    },
    downloadCount: {
      type: Number,
      default: 0
    },
    lastAccessedAt: {
      type: Date,
      default: null
    },
    
    // Soft delete
    isDeleted: {
      type: Boolean,
      default: false
    },
    deletedAt: {
      type: Date,
      default: null
    },
    deletedBy: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: 'deletedByModel'
    },
    deletedByModel: {
      type: String,
      enum: ['Agency', 'ModelUser']
    }
  },
  { 
    timestamps: true,
    // Add indexes for better query performance
    indexes: [
      { assignmentId: 1 },
      { modelId: 1, status: 1 },
      { agencyId: 1, status: 1 },
      { questionId: 1 },
      { generatedFilename: 1 },
      { cloudinaryPublicId: 1 },
      { uploadCompletedAt: -1 },
      { isLatestVersion: 1, version: -1 }
    ]
  }
);

// Virtual for file size in human readable format
voiceRecordingSchema.virtual('fileSizeFormatted').get(function() {
  const bytes = this.fileSize;
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
});

// Virtual for duration in human readable format
voiceRecordingSchema.virtual('durationFormatted').get(function() {
  const seconds = Math.floor(this.duration);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
});

// Virtual for upload time
voiceRecordingSchema.virtual('uploadTime').get(function() {
  if (!this.uploadCompletedAt || !this.uploadStartedAt) return null;
  return Math.floor((this.uploadCompletedAt - this.uploadStartedAt) / 1000); // seconds
});

// Pre-save middleware to handle version control
voiceRecordingSchema.pre('save', async function(next) {
  // If this is a new version, mark previous versions as not latest
  if (this.isNew && this.version > 1) {
    await this.constructor.updateMany(
      {
        assignmentId: this.assignmentId,
        _id: { $ne: this._id },
        isLatestVersion: true
      },
      { isLatestVersion: false }
    );
  }
  
  // Update upload completion time
  if (this.isModified('status') && this.status !== 'uploading' && !this.uploadCompletedAt) {
    this.uploadCompletedAt = new Date();
  }
  
  // Update submission time
  if (this.isModified('status') && this.status === 'submitted' && !this.submittedAt) {
    this.submittedAt = new Date();
  }
  
  next();
});

// Static method to find recordings by assignment
voiceRecordingSchema.statics.findByAssignment = function(assignmentId, latestOnly = true) {
  const query = { assignmentId, isDeleted: false };
  if (latestOnly) query.isLatestVersion = true;
  
  return this.find(query).sort({ version: -1, createdAt: -1 });
};

// Static method to find recordings by model
voiceRecordingSchema.statics.findByModel = function(modelId, status = null) {
  const query = { modelId, isDeleted: false, isLatestVersion: true };
  if (status) query.status = status;
  
  return this.find(query)
    .populate('assignmentId')
    .populate('questionId', 'text sectionId')
    .sort({ createdAt: -1 });
};

// Static method to get storage statistics
voiceRecordingSchema.statics.getStorageStats = function(agencyId = null) {
  const matchStage = { isDeleted: false };
  if (agencyId) matchStage.agencyId = agencyId;
  
  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalFiles: { $sum: 1 },
        totalSize: { $sum: '$fileSize' },
        totalDuration: { $sum: '$duration' },
        avgFileSize: { $avg: '$fileSize' },
        avgDuration: { $avg: '$duration' },
        formatBreakdown: {
          $push: '$format'
        }
      }
    }
  ]);
};

// Static method to generate filename for question recordings
voiceRecordingSchema.statics.generateQuestionFilename = function(questionId, modelId, assignmentId, timestamp = null) {
  const ts = timestamp || Date.now();
  return `${assignmentId}_${modelId}_${questionId}_${ts}.mp3`;
};

// Instance method to generate signed URL for download
voiceRecordingSchema.methods.generateDownloadUrl = function(expiresIn = 3600) {
  // Generate a simple signed URL for now
  // In production, this should use proper signed URLs from your storage provider
  const timestamp = Date.now() + (expiresIn * 1000);
  return `${this.fileUrl}?expires=${timestamp}&signature=${this._id}`;
};

// Alias method for backward compatibility
voiceRecordingSchema.methods.generateSignedDownloadUrl = function(expiresIn = 3600) {
  return this.generateDownloadUrl(expiresIn);
};

// Instance method to mark as accessed
voiceRecordingSchema.methods.markAccessed = function() {
  this.lastAccessedAt = new Date();
  this.downloadCount += 1;
  return this.save();
};

// Instance method to create new version
voiceRecordingSchema.methods.createNewVersion = function(newRecordingData) {
  const newVersion = new this.constructor({
    ...newRecordingData,
    assignmentId: this.assignmentId,
    questionId: this.questionId,
    questionText: this.questionText,
    modelId: this.modelId,
    agencyId: this.agencyId,
    version: this.version + 1,
    isLatestVersion: true
  });
  
  // Mark current version as replaced
  this.isLatestVersion = false;
  this.replacedBy = newVersion._id;
  
  return newVersion;
};

// Instance method to calculate quality score
voiceRecordingSchema.methods.calculateQualityScore = function() {
  if (!this.qualityMetrics) return null;
  
  const metrics = this.qualityMetrics;
  let score = 100;
  
  // Deduct points for issues
  if (metrics.noiseLevel > 30) score -= (metrics.noiseLevel - 30) * 0.5;
  if (metrics.volumeLevel < 40 || metrics.volumeLevel > 80) {
    score -= Math.abs(60 - metrics.volumeLevel) * 0.3;
  }
  if (metrics.clarityScore < 70) score -= (70 - metrics.clarityScore) * 0.4;
  if (metrics.backgroundNoise) score -= 10;
  if (metrics.clipping) score -= 15;
  if (metrics.silenceRatio > 20) score -= metrics.silenceRatio * 0.2;
  
  this.qualityScore = Math.max(0, Math.round(score));
  return this.qualityScore;
};

const VoiceRecording = mongoose.model("VoiceRecording", voiceRecordingSchema);
export default VoiceRecording;