import express from "express";
import dotenv from "dotenv";
import cors from "cors";
import connectDB from "./db.js";
import http from "http";
import { Server } from "socket.io";
import registerSocketHandlers from "./sockets/index.js";
import groupTopicMessageModel from "./models/Group/groupTopicMessageModel.js";
import { seedDefaultTemplate } from "./utils/seedDefaultTemplate.js";
import { seedQuestionTemplates } from "./utils/seedQuestionTemplates.js";
import path from "path";
import "./jobs/fetchtrends.js";
import cookieParser from "cookie-parser";
import ffmpegConfig from "./config/ffmpegConfig.js";

dotenv.config();

// Initialize database and seeding
(async () => {
  try {
    await connectDB();
    console.log("🌱 Database connected, starting seeding process...");
    
    // Seed default templates and questions after DB connection
    await seedDefaultTemplate();
    await seedQuestionTemplates();
    
    console.log("✅ Seeding completed successfully");
  } catch (error) {
    console.error("❌ Database connection or seeding failed:", error);
    process.exit(1);
  }
})();

// Initialize FFmpeg configuration
ffmpegConfig.initialize().catch(err => {
  console.warn("⚠️ FFmpeg initialization failed at startup:", err.message);
  console.warn("Voice recording features may not work properly");
});

const allowedOrigins = [
  "http://localhost:4000",
  "https://modelsuite-test.onrender.com", //TEST WEBSITE URL
  "https://modelsuite-ai.onrender.com", // LIVE WEBSITE URL
];

const corsOptions = {
  origin: function (origin, callback) {
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error("Not allowed by CORS"));
    }
  },
  credentials: true, // ✅ this allows cookies/auth headers
};

const app = express();
app.use(cors(corsOptions));

// app.set('trust proxy', 1);

app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use("/files", express.static("./public/files"));

// 🧩 Normal Routes
import modelRoutes from "./routes/modelRoutes.js";
import agencyRoutes from "./routes/agencyRoutes.js";
import uploadRoutes from "./routes/uploadRoutes.js";
import pandaDocRoutes from "./routes/pandadocroutes.js";
import instagramRoutes from "./routes/socialMedia/instagramRoutes.js";
import googleRoutes from "./routes/google/googleRoutes.js";
import eventRoutes from "./routes/google/eventRoutes.js";
import trendingroutes from "./routes/viraltrends/viralroute.js";
import ModelUser from "./models/model.js";
import Agency from "./models/agency.js";
import tiktokRoutes from "./routes/socialMedia/tiktokRoutes.js";
import chatRoutes from "./routes/messanger/chatRoute.js";
import dmRoutes from "./routes/messanger/dmRoutes.js";
import groupRoutes from "./routes/messanger/groupRoutes.js";
import channleRoutes from "./routes/messanger/dmRoutes.js";
import profileRoutes from "./routes/profileRoutes.js";
import contentUploadRoute from "./routes/contentupload/uploadroutes.js";
import magicLinkRoutes from "./routes/magicLinkRoutes.js";
import supportsystemroutes from "./routes/supportsystem/supportsystem.js";
import noteRoutes from "./routes/noteRoutes.js";
import voiceRoutes from "./routes/voice/voiceRoutes.js";

//magic-link routes
app.use("/api/v1", magicLinkRoutes);

// Questionnaire routes
import templateRoutes from "./routes/questionnaire/templateRoutes.js";
import assignmentRoutes from "./routes/questionnaire/assignmentRoutes.js";
import answerRoutes from "./routes/questionnaire/answerRoutes.js";
import boardRoutes from "./routes/task/boardRoutes.js";
import taskRoutes from "./routes/task/taskRoutes.js";

import billing from "./routes/billing/billing.js";
import TaskrouterBase from "twilio/lib/rest/TaskrouterBase.js";
import employeeRoutes from "./routes/Employee/employeeRoutes.js";

app.use("/api/v1/model", modelRoutes);
app.use("/api/v1/agency", agencyRoutes);
app.use("/api/v1/employee", employeeRoutes);
app.use("/api/v1/upload", uploadRoutes);
app.use("/api/v1/messanger/chat", chatRoutes);
app.use("/api/v1/messanger/dm", dmRoutes);
app.use("/api/v1/messanger/group", groupRoutes);
app.use("/api/v1/messanger/channel", channleRoutes);
app.use("/api/v1/panda", pandaDocRoutes);
app.use("/api/v1/board", boardRoutes);
app.use("/api/v1/tasks", taskRoutes);
app.use("/api/v1/instagram", instagramRoutes);
app.use("/api/v1/google", googleRoutes);
app.use("/api/v1/event", eventRoutes);
app.use("/api/v1/Trending", trendingroutes);
app.use("/api/v1/supportsystem", supportsystemroutes);
app.use("/api/v1/tiktok", tiktokRoutes);
app.use("/api/v1/profile", profileRoutes);
app.use("/api/v1/contentupload", contentUploadRoute);

// Questionnaire routes
app.use("/api/v1/questionnaire/templates", templateRoutes);
app.use("/api/v1/questionnaire/assignments", assignmentRoutes);
app.use("/api/v1/questionnaire/answers", answerRoutes);
app.use("/api/v1/billing", billing);
app.use("/api/v1/notes", noteRoutes);
app.use("/api/v1/voice", voiceRoutes);

// Health check endpoint for preventing cold starts and also saving the resources of render free tier
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "ok",
    timestamp: new Date().toISOString(),
  });
});

app.get("/", (req, res) => {
  res.send("ModelSuite API is running ✅"); // this is for docker
});

// 🧠 Create HTTP Server
const server = http.createServer(app);

// ⚡ Setup Socket.IO
const io = new Server(server, {
  cors: {
    origin: (origin, callback) => {
      const allowedOrigins = [
        "https://modelsuite-test.onrender.com", //TEST WEBSITE URL
        "https://modelsuite-ai.onrender.com", // LIVE WEBSITE URL
        "http://localhost:4000",
      ];

      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error("Not allowed by CORS"));
      }
    },
    methods: ["GET", "POST"],
    credentials: true,
  },
});

app.set("io", io);

// 🌐 In-memory user tracking
const connectedUsers = new Map();

io.on("connection", (socket) => {
  console.log("🔌 New socket connected:", socket.id);

  // ✅ Register user
  socket.on("register", ({ userId }) => {
    connectedUsers.set(userId, socket.id);
    socket.broadcast.emit("update_user_status", [
      {
        userId: userId,
        isOnline: true,
        lastOnline: null,
      },
    ]);
    console.log(`✅ Registered user ${userId} with socket ${socket.id}`);
  });

  // ✅ Handle all modular socket handlers
  registerSocketHandlers(io, socket, connectedUsers);

  /*
=========✅commenting all sockets related to messaging and writing new ones keeping this for backup  =========
  // ✅ Join a topic room (not group anymore)
  socket.on("join_topic", ({ topicId }) => {
    if (topicId) {
      socket.join(topicId);
      console.log(`✅ Socket ${socket.id} joined topic ${topicId}`);
    }
  });

  // ✅ Leave a topic room
  socket.on("leave_topic", ({ topicId }) => {
    if (topicId) {
      socket.leave(topicId);
      console.log(`🚪 Socket ${socket.id} left topic ${topicId}`);
    }
  });

  // ✅ Send group message inside a topic
  socket.on("send_group_message", async (newMessage) => {
    console.log("📩 Received group message:", newMessage);

    const { groupId, topicId, senderId, senderModel, text } = newMessage;

    if (!groupId || !topicId || !senderId || !text) {
      return console.error("❌ Missing message fields");
    }

    try {
      const saved = await groupTopicMessageModel.create({
        groupId,
        topicId,
        senderId,
        senderModel,
        text,
      });

      // Emit to topic room only
      io.to(topicId.toString()).emit("new_group_message", saved);
    } catch (err) {
      console.error("❌ Failed to save group message:", err.message);
    }
  });
  */

  // ✅ On user disconnect
  socket.on("disconnect", async () => {
    for (let [userId, sockId] of connectedUsers.entries()) {
      if (sockId === socket.id) {
        connectedUsers.delete(userId);

        // 🕒 Update lastOnline field
        try {
          await ModelUser.findByIdAndUpdate(userId, {
            lastOnline: new Date().toISOString(),
          });
          await Agency.findByIdAndUpdate(userId, {
            lastOnline: new Date().toISOString(),
          });
          // set user as offline
          socket.broadcast.emit("update_user_status", [
            {
              userId: userId,
              isOnline: false,
              lastOnline: new Date().toISOString(),
            },
          ]);
          console.log(`✅ Updated lastOnline for user ${userId}`);
        } catch (err) {
          console.error(
            `❌ Failed to update lastOnline for user ${userId}:`,
            err.message,
          );
        }

        break;
      }
    }
    console.log("❌ Socket disconnected:", socket.id);
  });

  // ✅ Cleanup all rooms
  socket.on("disconnecting", () => {
    for (let room of socket.rooms) {
      socket.leave(room);
    }
  });
});

// Global error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  
  // If error is already handled (has statusCode), use it
  if (err.statusCode) {
    return res.status(err.statusCode).json({
      success: false,
      message: err.message,
      errors: err.errors || []
    });
  }
  
  // For unhandled errors, return 500
  return res.status(500).json({
    success: false,
    message: process.env.NODE_ENV === 'production' ? 'Internal Server Error' : err.message,
    ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
  });
});

// 🚀 Start the server
server.listen(process.env.PORT, () => {
  console.log(`🚀 Server running on port ${process.env.PORT}`);
});

