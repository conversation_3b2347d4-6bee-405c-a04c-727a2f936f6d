{"name": "modelsuite-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fullcalendar/daygrid": "^6.1.18", "@fullcalendar/interaction": "^6.1.18", "@fullcalendar/react": "^6.1.18", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.10.0", "bson": "^6.10.4", "dayjs": "^1.11.13", "emoji-picker-react": "^4.12.2", "framer-motion": "^12.23.6", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash": "^4.17.21", "lucide-react": "^0.522.0", "moment": "^2.30.1", "nanoid": "^5.1.5", "react": "^19.1.0", "react-big-calendar": "^1.19.4", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "react-sortablejs": "^6.1.4", "recharts": "^2.15.4", "socket.io-client": "^4.8.1", "sonner": "^2.0.6", "sortablejs": "^1.15.6", "tailwind-scrollbar": "^3.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}