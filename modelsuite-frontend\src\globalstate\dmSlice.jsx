import { createSlice } from "@reduxjs/toolkit";
import socket from "../utils/socket";

const initialState = {
  conversations: [
    {
      convoId: "",
      type: "dm",
      members: [],
      createdBy: {},
      createdAt: "",
      messages: [],
      isLoading: true,
    },
  ],
  isLoading: true,
};

const dmSlice = createSlice({
  name: "dm",
  initialState,
  reducers: {
    setAllDmCoversations: (state, action) => {
      state.conversations = action.payload;
      state.isLoading = false;
    },
    addNewDmconversation: (state, action) => {
      state.conversations.push(action.payload[0]);
      action.payload[0].members.forEach((m) => {
        if (m.userId !== action.payload[1]) {
          socket.emit("fetch_user_status", [m.userId]);
        }
      });
    },
    addNewDmMessage: (state, action) => {
      const conversation = state.conversations.find(
        (c) => c.convoId == action.payload.convoId,
      );
      conversation.messages.push(action.payload);
    },
    setMessageSent: (state, action) => {
      const conversation = state.conversations.find(
        (c) => c.convoId == action.payload.convoId,
      );
      const message = conversation.messages.find(
        (m) => m._id === action.payload._id,
      );
      message.status = "sent";
    },
    setTyping: (state, action) => {
      const conversation = state.conversations.find(
        (c) => c.convoId == action.payload.convoId,
      );
      const member = conversation.members.find(
        (m) => m.userId === action.payload.userId,
      );
      member.status.isTyping = action.payload.state;
    },
    updateUserStatus: (state, action) => {
      const statusMap = new Map();
      action.payload.forEach((s) => {
        statusMap.set(s.userId, {
          isOnline: s.isOnline,
          lastOnline: s.lastOnline,
        });
      });

      state.conversations.forEach((convo) => {
        convo.members.forEach((member) => {
          if (statusMap.has(member.userId)) {
            const updatedValues = statusMap.get(member.userId);
            member.status = { ...member.status, ...updatedValues };
          }
        });
      });
    },
    setMessageSeen: (state, action) => {
      const { convoId, messageId } = action.payload;
      const conversation = state.conversations.find(
        (c) => c.convoId === convoId,
      );

      if (!conversation || !conversation.messages) return;

      const message = conversation.messages.find((m) => m._id === messageId);
      if (message) {
        message.status = "seen";
      }
    },
    addOlderMessages: (state, action) => {
      const { convoId, messages, hasMore } = action.payload;
      const convo = state.conversations.find((c) => c.convoId === convoId);
      if (convo) {
        convo.messages = [...messages, ...convo.messages]; // prepend
        convo.hasMore = hasMore;
      }
    },
    removeFailedMessage: (state, action) => {
      const { convoId, messageId } = action.payload;
      const convo = state.conversations.find((c) => c.convoId === convoId);
      const filteredConvoMessages = convo.messages.filter(
        (m) => m._id !== messageId,
      );
      convo.messages = filteredConvoMessages;
    },
    updateUploadForFile: (state, action) => {
      const { convoId, messageId, cloudinaryUrl, publicId, attachmentData } =
        action.payload;
      const convo = state.conversations.find((c) => c.convoId === convoId);
      if (!convo) return;

      const message = convo.messages.find((m) => m._id === messageId);
      if (!message) return;

      const attachment = message.attachments.find(
        (a) => a.fileId === attachmentData.fileId,
      );
      if (attachment) {
        attachment.status = attachmentData.status;
        attachment.progress = attachmentData.progress;
        attachment.cloudinaryUrl = cloudinaryUrl;
        attachment.publicId = publicId;
      }
    },
  },
});

export const {
  updateUploadForFile,
  removeFailedMessage,
  addOlderMessages,
  setMessageSeen,
  updateUserStatus,
  setTyping,
  setMessageSent,
  addNewDmMessage,
  setAllDmCoversations,
  addNewDmconversation,
} = dmSlice.actions;
export default dmSlice.reducer;
