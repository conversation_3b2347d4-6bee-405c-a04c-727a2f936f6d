import dayjs from "dayjs";
import { useRef } from "react";

export function useTypingIndicator(socket, userId, delay = 1000) {
  const typingTimeoutRef = useRef(null);
  const isTypingRef = useRef(false); // track current typing status

  const handleTyping = () => {
    // If not already typing, emit isTyping: true
    if (!isTypingRef.current) {
      socket.emit("typing", { userId, isTyping: true });
      isTypingRef.current = true;
    }

    // Clear existing timer
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Start new timer: after delay of no typing, send isTyping: false
    typingTimeoutRef.current = setTimeout(() => {
      socket.emit("typing", { userId, isTyping: false });
      isTypingRef.current = false; // reset flag
      typingTimeoutRef.current = null; // cleanup
    }, delay);
  };

  return handleTyping;
}

export function formateTime(TimeString, type) {
  let formattedTimeString;

  if (!TimeString) return;

  const rawdate = dayjs(TimeString);

  if (type === "msg") {
    formattedTimeString = `${rawdate.format("h:mm A")}`;
    return formattedTimeString;
  }

  if (dayjs().isSame(rawdate, "day")) {
    formattedTimeString = `Today ${rawdate.format("h:mm A")}`;
  } else if (dayjs().subtract(1, "day").isSame(rawdate, "day")) {
    formattedTimeString = `Yesterday ${rawdate.format("h:mm A")}`;
  } else {
    formattedTimeString = rawdate.format("M/D/YYYY h:mm A"); // e.g., 6/12/2024 1:45 PM
  }
  return formattedTimeString;
}

export const formatActiveTime = (minutes) => {
  if (!minutes) return "0h";
  if (minutes < 60) return `${minutes}m`;
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
};
