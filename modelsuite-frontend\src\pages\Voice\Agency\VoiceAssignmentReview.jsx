import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Download,
  Play,
  Pause,
  Star,
  CheckCircle,
  XCircle,
  Clock,
  ArrowLeft,
  FileText,
  Lightbulb,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { voiceAPI } from "../../../services/voiceAPI";

const VoiceAssignmentReview = () => {
  const { assignmentId } = useParams();
  const navigate = useNavigate();

  const [assignment, setAssignment] = useState(null);
  const [recordings, setRecordings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [playingAudio, setPlayingAudio] = useState(null);
  const [reviewing, setReviewing] = useState(false);
  const [reviewForm, setReviewForm] = useState({
    status: "approved",
    comments: "",
    rating: 5,
  });

  useEffect(() => {
    if (assignmentId) {
      fetchAssignmentDetails();
    }
  }, [assignmentId]);

  const fetchAssignmentDetails = async () => {
    try {
      setLoading(true);
      const [assignmentResponse, recordingsResponse] = await Promise.all([
        voiceAPI.getAssignmentById(assignmentId),
        voiceAPI.getAssignmentRecordings(assignmentId),
      ]);

      setAssignment(assignmentResponse.data.assignment);
      setRecordings(recordingsResponse.data || []);
    } catch (error) {
      console.error("Error fetching assignment details:", error);
      toast.error("Failed to load assignment details");
      navigate("/agency/dashboard");
    } finally {
      setLoading(false);
    }
  };

  const handlePlayRecording = async (recording) => {
    if (playingAudio === recording._id) {
      setPlayingAudio(null);
      // Stop audio
    } else {
      setPlayingAudio(recording._id);
      // Play audio from recording.fileUrl
      try {
        const audio = new Audio(recording.fileUrl);
        audio.play();
        audio.onended = () => setPlayingAudio(null);
      } catch (error) {
        console.error("Error playing audio:", error);
        toast.error("Failed to play recording");
        setPlayingAudio(null);
      }
    }
  };

  const handleDownloadRecordings = async () => {
    try {
      const response = await voiceAPI.downloadAssignmentRecordings(
        assignmentId
      );

      if (response.data.downloadUrls) {
        // Multiple files - download each one
        response.data.downloadUrls.forEach((file, index) => {
          setTimeout(() => {
            const link = document.createElement("a");
            link.href = file.url;
            link.download = file.filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }, index * 1000); // Stagger downloads by 1 second
        });
        toast.success(`Downloading ${response.data.totalFiles} recordings`);
      } else {
        // Single file download
        toast.success("Download started");
      }
    } catch (error) {
      console.error("Error downloading recordings:", error);
      toast.error("Failed to download recordings");
    }
  };

  const handleReviewSubmit = async (recordingId, status) => {
    try {
      setReviewing(true);
      await voiceAPI.reviewRecording(recordingId, {
        status,
        comments: reviewForm.comments,
        rating: status === "approved" ? reviewForm.rating : null,
      });

      toast.success(`Recording ${status} successfully`);
      fetchAssignmentDetails(); // Refresh data
      setReviewForm({ status: "approved", comments: "", rating: 5 });
    } catch (error) {
      console.error("Error reviewing recording:", error);
      toast.error("Failed to submit review");
    } finally {
      setReviewing(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "approved":
        return "bg-green-100 text-green-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      case "submitted":
        return "bg-blue-100 text-blue-800";
      case "requires_revision":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!assignment) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Assignment not found</p>
      </div>
    );
  }

  const questionType =
    assignment.questionIds?.length > 0 ? "questions" : "unknown";

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate("/agency/dashboard")}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                Assignment Review
              </h1>
              <p className="text-sm text-gray-600">
                Review and manage model recordings
              </p>
            </div>
          </div>

          {recordings.length > 0 && (
            <button
              onClick={handleDownloadRecordings}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <Download className="w-4 h-4" />
              Download All
            </button>
          )}
        </div>
      </div>

      <div className="p-6 max-w-7xl mx-auto">
        {/* Assignment Info Card */}
        <div className="bg-white rounded-lg p-6 mb-6 shadow-sm">
          <div className="flex items-start justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                {assignment.title}
              </h2>
              <p className="text-gray-600 mb-4">{assignment.description}</p>

              <div className="flex items-center gap-4 text-sm text-gray-600">
                <span>Model: {assignment.modelId.username}</span>
                <span>•</span>
                <span>
                  Assigned:{" "}
                  {new Date(assignment.assignedAt).toLocaleDateString()}
                </span>
                <span>•</span>
                <span>Status: {assignment.status}</span>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <span
                className={`px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800`}
              >
                <FileText className="w-4 h-4 inline mr-1" />
                Question Assignment ({assignment.questionIds?.length || 0}{" "}
                questions)
              </span>

              <span
                className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                  assignment.status
                )}`}
              >
                {assignment.status}
              </span>
            </div>
          </div>

          {assignment.questionIds && assignment.questionIds.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <h3 className="font-medium text-blue-900 mb-2">
                Questions ({assignment.questionIds.length})
              </h3>
              <div className="space-y-2">
                {assignment.questionIds.map((question, index) => (
                  <div key={question._id} className="text-blue-800">
                    <span className="font-medium">Q{index + 1}:</span>{" "}
                    {question.text}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Recordings Section */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="px-6 py-4 border-b">
            <h3 className="text-lg font-semibold text-gray-900">
              Recordings ({recordings.length})
            </h3>
          </div>

          {recordings.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <Clock className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>No recordings submitted yet</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {recordings.map((recording, index) => (
                <RecordingReviewItem
                  key={recording._id}
                  recording={recording}
                  index={index}
                  scriptType={scriptType}
                  assignment={assignment}
                  playingAudio={playingAudio}
                  onPlay={() => handlePlayRecording(recording)}
                  onReview={(status) =>
                    handleReviewSubmit(recording._id, status)
                  }
                  reviewing={reviewing}
                  reviewForm={reviewForm}
                  setReviewForm={setReviewForm}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Individual Recording Review Component
const RecordingReviewItem = ({
  recording,
  index,
  scriptType,
  assignment,
  playingAudio,
  onPlay,
  onReview,
  reviewing,
  reviewForm,
  setReviewForm,
}) => {
  const [showReviewForm, setShowReviewForm] = useState(false);
  const isPlaying = playingAudio === recording._id;

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const formatFileSize = (bytes) => {
    const kb = bytes / 1024;
    const mb = kb / 1024;
    return mb > 1 ? `${mb.toFixed(1)} MB` : `${kb.toFixed(1)} KB`;
  };

  return (
    <div className="p-6">
      <div className="flex items-start gap-4">
        {/* Recording Info */}
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-3">
            <h4 className="font-medium text-gray-900">
              {scriptType === "detailed"
                ? `Sentence ${index + 1}`
                : "Single Line Recording"}
            </h4>
            <span
              className={`px-2 py-1 rounded-full text-xs font-medium ${
                recording.status === "approved"
                  ? "bg-green-100 text-green-800"
                  : recording.status === "rejected"
                  ? "bg-red-100 text-red-800"
                  : recording.status === "submitted"
                  ? "bg-blue-100 text-blue-800"
                  : "bg-gray-100 text-gray-800"
              }`}
            >
              {recording.status}
            </span>
          </div>

          {/* Content */}
          {scriptType === "detailed" ? (
            <p className="text-gray-700 mb-3">
              {recording.sentenceText || "Sentence content not available"}
            </p>
          ) : (
            <div className="mb-3">
              <p className="text-sm text-gray-600 mb-1">Completed Prompt:</p>
              <p className="text-gray-800 italic">
                "{recording.completedPrompt || "Completion not available"}"
              </p>
            </div>
          )}

          {/* Metadata */}
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <span>Duration: {formatDuration(recording.duration || 0)}</span>
            <span>•</span>
            <span>Size: {formatFileSize(recording.fileSize || 0)}</span>
            <span>•</span>
            <span>
              Uploaded:{" "}
              {new Date(recording.uploadCompletedAt).toLocaleDateString()}
            </span>
          </div>

          {/* Review Comments */}
          {recording.reviewComments && (
            <div className="mt-3 p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-700">
                {recording.reviewComments}
              </p>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          <button
            onClick={onPlay}
            className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg"
          >
            {isPlaying ? (
              <Pause className="w-5 h-5" />
            ) : (
              <Play className="w-5 h-5" />
            )}
          </button>

          {recording.status === "submitted" && (
            <>
              <button
                onClick={() => onReview("approved")}
                disabled={reviewing}
                className="flex items-center gap-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
              >
                <CheckCircle className="w-4 h-4" />
                Approve
              </button>
              <button
                onClick={() => setShowReviewForm(!showReviewForm)}
                className="flex items-center gap-1 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                <XCircle className="w-4 h-4" />
                Review
              </button>
            </>
          )}

          {recording.rating && (
            <div className="flex items-center gap-1 px-2 py-1 bg-yellow-100 rounded-lg">
              <Star className="w-4 h-4 text-yellow-600" />
              <span className="text-sm font-medium text-yellow-800">
                {recording.rating}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Review Form */}
      {showReviewForm && (
        <div className="mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
          <h5 className="font-medium text-gray-900 mb-3">Review Recording</h5>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={reviewForm.status}
                onChange={(e) =>
                  setReviewForm((prev) => ({ ...prev, status: e.target.value }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              >
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
                <option value="requires_revision">Requires Revision</option>
              </select>
            </div>

            {reviewForm.status === "approved" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Rating
                </label>
                <select
                  value={reviewForm.rating}
                  onChange={(e) =>
                    setReviewForm((prev) => ({
                      ...prev,
                      rating: parseInt(e.target.value),
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                >
                  <option value={5}>5 - Excellent</option>
                  <option value={4}>4 - Good</option>
                  <option value={3}>3 - Average</option>
                  <option value={2}>2 - Below Average</option>
                  <option value={1}>1 - Poor</option>
                </select>
              </div>
            )}
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Comments
            </label>
            <textarea
              value={reviewForm.comments}
              onChange={(e) =>
                setReviewForm((prev) => ({ ...prev, comments: e.target.value }))
              }
              placeholder="Add your feedback..."
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="flex gap-2">
            <button
              onClick={() => onReview(reviewForm.status)}
              disabled={reviewing}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {reviewing ? "Submitting..." : "Submit Review"}
            </button>
            <button
              onClick={() => setShowReviewForm(false)}
              className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default VoiceAssignmentReview;
