import nodemailer from "nodemailer";

export const sendOtpToEmail = async (email, otp) => {
  try {
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: email,
      subject: "OTP for ModelSuite.ai",
      text: `Your OTP for is: ${otp}. It expires in 10 minutes.`,
    });

    // console.log(`OTP sent to email: ${email}`);
  } catch (error) {
    console.error("Failed to send OTP via Email:", error.message);
    throw new Error("<PERSON><PERSON> failed");
  }
};

export const sendEmployeeWelcomeEmail = async (to, name, tempPassword) => {
  try {
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    const loginURL = `${process.env.FRONTEND_HOSTING_BASEURL}/agency/login`;

    await transporter.sendMail({
      from: `"ModelSuite" <${process.env.EMAIL_USER}>`,
      to,
      subject: "Your Employee Account on ModelSuite.ai",
      html: `
        <div style="font-family: Arial, sans-serif; padding: 10px;">
          <h2>Hi ${name},</h2>
          <p>You’ve been added as an employee to your agency on <strong>ModelSuite.ai</strong>.</p>
          <p><strong>Login:</strong> <a href="${loginURL}">${loginURL}</a></p>
          <p><strong>Temporary Password:</strong> ${tempPassword}</p>
          <p>Please log in and change your password immediately for security.</p>
          <br/>
          <p>– The ModelSuite Team</p>
        </div>
      `,
    });
  } catch (error) {
    console.error("Failed to send employee welcome email:", error.message);
    throw new Error("Email sending failed");
  }
};

export const sendEmployeeInviteEmail = async (to, activationLink) => {
  try {
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    await transporter.sendMail({
      from: `"ModelSuite" <${process.env.EMAIL_USER}>`,
      to,
      subject: "You've been invited to join ModelSuite.ai",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2>Welcome to ModelSuite! 🎉</h2>
          <p>You've been invited to join a team on ModelSuite.ai. To get started, click the button below to activate your account:</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${activationLink}" 
               style="background-color: #4F46E5; color: white; padding: 12px 24px; 
                      text-decoration: none; border-radius: 6px; font-weight: bold;">
              Activate Your Account
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px;">
            This invite link will expire in 24 hours. If you don't recognize this invitation, 
            please ignore this email.
          </p>
          
          <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
          
          <p style="color: #666; font-size: 12px;">
            – The ModelSuite Team
          </p>
        </div>
      `,
    });
  } catch (error) {
    console.error("Failed to send invite email:", error.message);
    throw new Error("Email sending failed");
  }
};
