import React from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import VoiceAssignments from "./Model/VoiceAssignments.jsx";
import VoiceReviewCenter from "./Agency/VoiceReviewCenter.jsx";
import VoiceAssignmentReview from "./Agency/VoiceAssignmentReview.jsx";
import VoiceScriptManagement from "./Agency/VoiceScriptManagement.jsx";
import VoiceAnalytics from "./Agency/VoiceAnalytics.jsx";
import VoiceExportHub from "./Agency/VoiceExportHub.jsx";

const VoiceRoutes = () => {
  return (
    <Routes>
      {/* Model Routes */}
      <Route path="/assignments" element={<VoiceAssignments />} />

      {/* Agency Routes */}
      <Route path="/scripts" element={<VoiceScriptManagement />} />
      <Route path="/review-center" element={<VoiceReviewCenter />} />
      <Route path="/analytics" element={<VoiceAnalytics />} />
      <Route path="/export" element={<VoiceExportHub />} />
      <Route
        path="/assignment/:id/review"
        element={<VoiceAssignmentReview />}
      />

      {/* Default redirects */}
      <Route path="/" element={<Navigate to="assignments" replace />} />
      <Route
        path="/dashboard"
        element={<Navigate to="assignments" replace />}
      />
      <Route
        path="/script-management"
        element={<Navigate to="scripts" replace />}
      />
      <Route path="/review" element={<Navigate to="review-center" replace />} />
      <Route path="*" element={<Navigate to="assignments" replace />} />
    </Routes>
  );
};

export default VoiceRoutes;
