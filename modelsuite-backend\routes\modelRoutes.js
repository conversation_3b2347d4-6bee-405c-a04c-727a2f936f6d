import express from "express";
import {
  sendOtpForRegistration,
  verifyOtpAndRegister,
  login,
  verifyOtpAndLogin,
  getModelById,
  getAllModels,
  toggleMfa,
  deleteModel,
  forgotPasswordSendOtp,
  forgotPasswordVerifyOtp,
  isAcceptingEmail,
  logoutUser,
  refreshAccessToken,
  changeModelEmail,
  verifyNewModelEmail,
  changePasswordSendOtp,
  verifyOtpAndChangePassword,
} from "../controllers/modelController.js";
import { verifyRole, verifyToken } from "../middlewares/authMiddleware.js";

// //rate-limiter middlewares
// import { authLimiter } from "../middlewares/limiters/authLimiter.middleware.js";
// import { apiLimiter } from "../middlewares/limiters/apiLimiter.middleware.js";
// import { otpLimiter } from "../middlewares/limiters/otpLimiter.middlerware.js";
// import { strictLimiter } from "../middlewares/limiters/strictLimiter.middleware.js";

const router = express.Router();

// router.post("/register/send-otp", otpLimiter, sendOtpForRegistration);
router.post("/register/send-otp", sendOtpForRegistration);

// router.post("/register/verify-otp",authLimiter, verifyOtpAndRegister);
router.post("/register/verify-otp", verifyOtpAndRegister);

router.post("/login", login);
router.post("/login/verify-otp", verifyOtpAndLogin);

router.post("/forgot-password", forgotPasswordSendOtp);
router.post("/forgot-password/verify-otp", forgotPasswordVerifyOtp);

router.get("/:id", verifyToken, getModelById);
router.get("/", verifyToken, verifyRole("model"), getAllModels);
router.post("/toggle-mfa", verifyToken, toggleMfa);
router.post("/logout", verifyToken, logoutUser);
router.post("/refresh-access-token", refreshAccessToken);

router.patch("/change-email", verifyToken, changeModelEmail);
router.post("/verify-new-email", verifyToken, verifyNewModelEmail);

router.patch("/change-password", verifyToken, changePasswordSendOtp);
router.post("/change-password-verify", verifyToken, verifyOtpAndChangePassword);

router.delete("/delete-model", deleteModel);

router.post("/update-is-accepting-email", verifyToken, isAcceptingEmail);

export default router;
