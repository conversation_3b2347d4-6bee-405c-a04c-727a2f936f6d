import express from "express";
import {
  sendOtpForRegistration,
  verifyOtpAndRegister,
  verifyOtpAndLoginAgency,
  loginAgency,
  searchModels,
  getAgencyModels,
  debugModels,
  assignAllModels,
  addModelToAgency,
  getAgencyDetails,
  toggleMfa,
  forgotPasswordSendOtp,
  forgotPasswordVerifyOtp,
  deleteAgency,
  updateAgencyDetails,
  logoutUser,
  getCustomInserts,
  updateCustomInserts,
  updateModelOrder,
  getModelGroups,
  updateModelGroups,
  updateModelGroupAssignment,
  changeAgencyEmail,
  verifyNewAgencyEmail,
  changePasswordSendOtp,
  verifyOtpAndChangePassword,
  refreshAccessToken,
} from "../controllers/agencyController.js";
import { verifyRole, verifyToken } from "../middlewares/authMiddleware.js";
import upload from "../middlewares/multer.js";
import Agency from "../models/agency.js";

// //rate-limiter middlewares
// import { authLimiter } from "../middlewares/limiters/authLimiter.middleware.js";
// import { apiLimiter } from "../middlewares/limiters/apiLimiter.middleware.js";
// import { otpLimiter } from "../middlewares/limiters/otpLimiter.middlerware.js";
// import { strictLimiter } from "../middlewares/limiters/strictLimiter.middleware.js";

const router = express.Router();

router.post("/register/send-otp", sendOtpForRegistration);
router.post("/register/verify-otp", verifyOtpAndRegister);
router.post("/login", loginAgency);
router.post("/login/verify-otp", verifyOtpAndLoginAgency);
router.post("/toggle-mfa", verifyToken, toggleMfa);
router.post("/forgot-password", forgotPasswordSendOtp);
router.post("/forgot-password/verify-otp", forgotPasswordVerifyOtp);
router.post("/logout", verifyToken, logoutUser);
router.patch("/change-email", verifyToken, changeAgencyEmail);
router.post("/verify-new-email", verifyToken, verifyNewAgencyEmail);
router.patch("/change-password", verifyToken, changePasswordSendOtp);
router.post("/change-password-verify", verifyToken, verifyOtpAndChangePassword);
router.post("/refresh-access-token", verifyToken, refreshAccessToken);
router.get("/models", verifyToken, verifyRole("agency"), searchModels);
router.get(
  "/agency-models",
  verifyToken,
  // verifyRole("agency"),
  getAgencyModels,
);
// Debug endpoints for troubleshooting model assignment issues
router.get("/debug-models", verifyToken, verifyRole("agency"), debugModels);
router.post(
  "/assign-all-models",
  verifyToken,
  verifyRole("agency"),
  assignAllModels,
);
router.post("/add-model", verifyToken, verifyRole("agency"), addModelToAgency);
router.delete("/delete-agency", deleteAgency);
router.get(
  "/getAgencyDetail",
  verifyToken,
  verifyRole("agency"),
  getAgencyDetails,
);
router.patch(
  "/update-agency-settings",
  verifyToken,
  verifyRole("agency"),
  upload.fields([
    { name: "logo" },
    { name: "bannerImage" },
    { name: "trustBadge" },
    { name: "teamMemberAvatar0" },
    { name: "teamMemberAvatar1" },
    { name: "teamMemberAvatar2" },
  ]),
  updateAgencyDetails,
);

// Custom inserts routes
router.get("/custom-inserts", verifyToken, getCustomInserts);
router.put("/custom-inserts", verifyToken, updateCustomInserts);

// Add this route with the other agency routes
router.put("/model-order", verifyToken, verifyRole("agency"), updateModelOrder);

// Add these routes after your existing routes
router.get("/model-groups", verifyToken, verifyRole("agency"), getModelGroups);
router.put(
  "/model-groups",
  verifyToken,
  verifyRole("agency"),
  updateModelGroups,
);
router.put(
  "/model-group-assignment",
  verifyToken,
  verifyRole("agency"),
  updateModelGroupAssignment,
);

export default router;
