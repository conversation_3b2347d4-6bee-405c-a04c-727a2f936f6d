import React from "react";

const Pagination = ({ page, totalPages, onPrev, onNext }) => {
  return (
    <div className="flex items-center justify-center mt-8">
      <div className="flex items-center gap-4 px-6 py-3 bg-gray-800 rounded-2xl shadow-md border border-gray-700">
        <button
          onClick={onPrev}
          disabled={page === 1}
          className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 
            ${
              page === 1
                ? "bg-gray-600 text-gray-300 cursor-not-allowed"
                : "bg-gray-700 hover:bg-gray-600 text-white"
            }`}
        >
          ← Previous
        </button>

        <span className="text-sm font-semibold text-gray-300">
          Page <span className="text-white">{page}</span> of{" "}
          <span className="text-white">{totalPages}</span>
        </span>

        <button
          onClick={onNext}
          disabled={page === totalPages}
          className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 
            ${
              page === totalPages
                ? "bg-gray-600 text-gray-300 cursor-not-allowed"
                : "bg-gray-700 hover:bg-gray-600 text-white"
            }`}
        >
          Next →
        </button>
      </div>
    </div>
  );
};

export default Pagination;
