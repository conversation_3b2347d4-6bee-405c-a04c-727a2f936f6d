import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Line,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

const data = [
  { name: "<PERSON>", value: 1200 },
  { name: "<PERSON><PERSON>", value: 2100 },
  { name: "Wed", value: 800 },
  { name: "<PERSON>hu", value: 1600 },
  { name: "<PERSON><PERSON>", value: 2600 },
  { name: "Sat", value: 3200 },
  { name: "Sun", value: 1800 },
];

const Dashboard = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#f9fafb] to-[#e5e7eb] p-10 font-sans text-gray-800">
      <header className="text-center mb-16">
        <h1 className="text-6xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-blue-700 to-purple-600">
          🌍 Executive Dashboard
        </h1>
        <p className="text-gray-500 mt-4 text-lg">
          Monitor your performance in real time with precision
        </p>
      </header>

      <section className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-8 mb-16">
        {[
          {
            title: "Outstanding",
            value: "₹10,000",
            color: "text-red-500",
            icon: "📤",
          },
          {
            title: "Earnings",
            value: "₹30,000",
            color: "text-green-600",
            icon: "💰",
          },
          {
            title: "Paid",
            value: "₹20,000",
            color: "text-blue-600",
            icon: "✅",
          },
          {
            title: "Pending",
            value: "₹5,000",
            color: "text-yellow-500",
            icon: "⏳",
          },
        ].map(({ title, value, color, icon }, i) => (
          <div
            key={i}
            className="bg-white p-6 rounded-3xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 flex items-center gap-5"
          >
            <div className="text-5xl">{icon}</div>
            <div>
              <h2 className="text-sm uppercase tracking-wider text-gray-500 font-medium">
                {title}
              </h2>
              <p className={`text-3xl font-extrabold ${color}`}>{value}</p>
            </div>
          </div>
        ))}
      </section>

      <section className="grid grid-cols-1 xl:grid-cols-3 gap-10 mb-16">
        <div className="xl:col-span-2 bg-white p-8 rounded-3xl shadow-xl border border-gray-100">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-2xl font-bold text-gray-800">
              📈 Weekly Revenue Overview
            </h3>
          </div>
          <ResponsiveContainer width="100%" height={250}>
            <LineChart data={data}>
              <XAxis dataKey="name" stroke="#9CA3AF" />
              <YAxis stroke="#9CA3AF" />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#6366F1"
                strokeWidth={3}
                dot={{ r: 5 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        <div className="bg-white p-8 rounded-3xl shadow-xl border border-gray-100">
          <h3 className="text-2xl font-bold text-gray-800 mb-6">
            🧾 Tax Summary
          </h3>
          <p className="text-gray-600 text-lg mb-2">
            GST Collected:{" "}
            <span className="text-green-600 font-semibold">₹4,500</span>
          </p>
          <p className="text-gray-600 text-lg">
            GST Paid: <span className="text-red-600 font-semibold">₹2,000</span>
          </p>
        </div>
      </section>

      <section className="grid grid-cols-1 xl:grid-cols-3 gap-10">
        <div className="bg-white p-8 rounded-3xl shadow-xl border border-gray-100 xl:col-span-2">
          <h3 className="text-2xl font-bold text-gray-800 mb-6">
            📝 Latest Activities
          </h3>
          <ul className="space-y-4">
            {[
              "Invoice #123 marked as paid",
              "Invoice #124 sent to client",
              "Invoice #125 overdue",
            ].map((activity, i) => (
              <li
                key={i}
                className="p-4 bg-gray-50 border border-gray-200 rounded-xl hover:bg-gray-100 transition-all text-gray-700"
              >
                {activity}
              </li>
            ))}
          </ul>
        </div>

        <div className="bg-white p-8 rounded-3xl shadow-xl border border-gray-100">
          <h3 className="text-2xl font-bold text-gray-800 mb-6">
            📄 Recent Transactions
          </h3>
          <div className="overflow-x-auto">
            <table className="min-w-full text-left text-sm">
              <thead className="text-gray-500 border-b">
                <tr>
                  <th className="pb-2">Invoice ID</th>
                  <th className="pb-2">Amount</th>
                  <th className="pb-2">Status</th>
                  <th className="pb-2">Date</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100">
                {[
                  {
                    id: "INV-123",
                    amount: "₹4,500",
                    status: "Paid",
                    color: "text-green-600",
                    date: "20 July 2025",
                  },
                  {
                    id: "INV-124",
                    amount: "₹3,000",
                    status: "Pending",
                    color: "text-yellow-600",
                    date: "21 July 2025",
                  },
                  {
                    id: "INV-125",
                    amount: "₹2,500",
                    status: "Overdue",
                    color: "text-red-600",
                    date: "22 July 2025",
                  },
                ].map(({ id, amount, status, color, date }, i) => (
                  <tr key={i} className="hover:bg-gray-50">
                    <td className="py-3 font-medium text-gray-800">{id}</td>
                    <td className="py-3 text-gray-800">{amount}</td>
                    <td className={`py-3 font-semibold ${color}`}>{status}</td>
                    <td className="py-3 text-gray-600">{date}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Dashboard;
